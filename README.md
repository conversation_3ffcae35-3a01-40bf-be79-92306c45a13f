# 📈 Stock Market Demo App

A complete Flutter demo application that simulates a mini stock market with Firebase Remote Config and Shorebird OTA updates.

## ✨ Features

### 🏪 Share Market Simulation
- **Predefined Shares**: TATA, RELIANCE, INFY, TCS, HDFC, ICICI, WIPRO, BHARTI, ITC, SBIN
- **Real-time Prices**: Fetched dynamically via Firebase Remote Config
- **Buy/Sell Operations**: Complete transaction system with validation
- **Portfolio Management**: Track owned shares and portfolio value
- **Transaction History**: View all buy/sell transactions

### 🔥 Firebase Remote Config Integration
- **Dynamic Share Prices**: Update prices without app updates
- **Feature Toggles**:
  - `enable_sell_option`: Show/hide sell buttons
  - `enable_sorting`: Enable share list sorting
  - `enable_market_trend`: Show market trend indicators
- **Market Status**: Control market open/closed state
- **Transaction Limits**: Configure min/max transaction amounts

### 🚀 Shorebird OTA Updates
- **Code Push**: Update Dart code without app store releases
- **Feature Updates**: Add new sorting, filtering, or UI changes
- **Business Logic**: Update buy/sell flow or portfolio calculations
- **Instant Deployment**: Changes reflect immediately after patch

### 📱 Manual Distribution
- **No App Store**: Direct APK/IPA installation
- **Local Testing**: Build and test locally
- **Enterprise Distribution**: Perfect for internal apps

## 🛠️ Setup Instructions

### Prerequisites
- Flutter SDK (3.8.1 or higher)
- Firebase account
- Shorebird CLI
- Android Studio / Xcode (for mobile development)

### 1. Firebase Setup

#### Create Firebase Project
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Create a new project: `stock-market-demo`
3. Enable Firebase Remote Config

#### Add Firebase to Android
1. In Firebase Console, add Android app
2. Package name: `com.example.shorebird_demo`
3. Download `google-services.json`
4. Place it in `android/app/google-services.json`

#### Add Firebase to iOS
1. In Firebase Console, add iOS app
2. Bundle ID: `com.example.shorebirdDemo`
3. Download `GoogleService-Info.plist`
4. Place it in `ios/Runner/GoogleService-Info.plist`

#### Configure Remote Config Parameters
In Firebase Console → Remote Config, add these parameters:

**Share Prices:**
```
price_TATA: 1250.50
price_RELIANCE: 2890.75
price_INFY: 1680.25
price_TCS: 3750.80
price_HDFC: 1580.40
price_ICICI: 1120.60
price_WIPRO: 580.30
price_BHARTI: 920.15
price_ITC: 485.90
price_SBIN: 820.45
```

**Feature Toggles:**
```
enable_sell_option: true
enable_sorting: false
enable_market_trend: false
market_status: "OPEN"
maintenance_mode: false
```

**App Configuration:**
```
min_transaction_amount: 100.0
max_transaction_amount: 100000.0
transaction_fee_percentage: 0.1
```

### 2. Shorebird Setup

#### Install Shorebird CLI
```bash
# Install Shorebird
curl --proto '=https' --tlsv1.2 https://shorebird.dev/install.sh -sSf | bash

# Add to PATH (add to your shell profile)
export PATH="$HOME/.shorebird/bin:$PATH"

# Verify installation
shorebird --version
```

#### Initialize Shorebird Project
```bash
# Login to Shorebird
shorebird login

# Initialize project (run in project root)
shorebird init

# This will update shorebird.yaml with your app_id
```

### 3. Build and Install

#### Android APK
```bash
# Build release APK with Shorebird
shorebird build apk

# Install on device
adb install build/app/outputs/flutter-apk/app-release.apk
```

#### iOS IPA
```bash
# Build iOS release
shorebird build ipa

# Install via Xcode or device provisioning
# The IPA will be in build/ios/ipa/
```

### 4. Push OTA Updates

#### Example: Enable Sorting Feature
1. In Firebase Console, set `enable_sorting: true`
2. Test the change (sorting options should appear)

#### Example: Add Market Trend Feature
1. In Firebase Console, set `enable_market_trend: true`
2. Push code update:
```bash
# Make code changes (already implemented in the app)
# Push the update
shorebird patch android
shorebird patch ios
```

#### Example: Update Share Prices
1. In Firebase Console, update any `price_*` parameter
2. Changes reflect immediately in the app (no code push needed)

## 🧪 Testing Remote Config Updates

### Test Price Updates
1. Open Firebase Console → Remote Config
2. Change `price_TATA` from `1250.50` to `1300.00`
3. Publish changes
4. In the app, pull to refresh or wait for auto-update
5. TATA price should update to ₹1300.00

### Test Feature Toggle
1. Set `enable_sell_option: false` in Remote Config
2. Publish changes
3. Sell buttons should disappear from the app
4. Set back to `true` to re-enable

### Test Market Status
1. Set `market_status: "CLOSED"` in Remote Config
2. Market status banner should show "Market CLOSED"
3. Set back to `"OPEN"` to restore normal operation

## 🚀 Pushing Code Updates with Shorebird

### Scenario 1: Add New Sorting Option
```bash
# The sorting feature is already implemented
# Just enable it via Remote Config
# Set enable_sorting: true in Firebase Console
```

### Scenario 2: Update UI Colors
```bash
# Make changes to colors in the code
# Push the update
shorebird patch android
shorebird patch ios
```

### Scenario 3: Modify Transaction Logic
```bash
# Update buy/sell logic in lib/services/stock_market_service.dart
# Push the update
shorebird patch android
shorebird patch ios
```

## 📁 Project Structure

```
lib/
├── main.dart                          # App entry point
├── models/
│   ├── share.dart                     # Share data model
│   └── user_portfolio.dart            # Portfolio and transaction models
├── services/
│   ├── firebase_remote_config_service.dart  # Remote Config integration
│   ├── local_storage_service.dart     # Local data persistence
│   └── stock_market_service.dart      # Main business logic
├── providers/
│   └── stock_market_provider.dart     # State management
├── screens/
│   ├── share_list_screen.dart         # Main shares list
│   └── portfolio_screen.dart          # Portfolio and transactions
└── widgets/
    ├── share_card.dart                # Individual share display
    ├── buy_sell_dialog.dart           # Transaction dialog
    ├── portfolio_holding_card.dart    # Portfolio item display
    └── transaction_history_card.dart  # Transaction history item
```

## 🔧 Development Commands

```bash
# Install dependencies
flutter pub get

# Run in debug mode
flutter run

# Build for release
shorebird build apk
shorebird build ipa

# Push OTA updates
shorebird patch android
shorebird patch ios

# Check patch status
shorebird patch list
```

## 🎯 Key Features Demonstration

### 1. Dynamic Pricing
- Share prices update from Firebase Remote Config
- No app restart required
- Automatic refresh every minute

### 2. Feature Toggles
- Sell button visibility controlled remotely
- Sorting options can be enabled/disabled
- Market trend indicators toggle

### 3. OTA Updates
- UI changes pushed instantly
- Business logic updates without app store
- New features deployed seamlessly

### 4. Portfolio Management
- Real-time portfolio value calculation
- Profit/loss tracking
- Transaction history with timestamps

## 🐛 Troubleshooting

### Firebase Issues
- Ensure `google-services.json` and `GoogleService-Info.plist` are correctly placed
- Check Firebase project configuration
- Verify Remote Config parameters are published

### Shorebird Issues
- Ensure you're logged in: `shorebird login`
- Check app is initialized: `shorebird init`
- Verify build succeeds before patching

### Build Issues
- Run `flutter clean && flutter pub get`
- Check Flutter and Dart SDK versions
- Ensure all dependencies are compatible

## 📝 Notes

- The app works offline with cached data
- Firebase Remote Config has default values as fallback
- Shorebird patches only affect Dart code, not native code
- Manual distribution means no app store approval process

## 🤝 Contributing

This is a demo application showcasing Flutter, Firebase Remote Config, and Shorebird integration. Feel free to extend it with additional features like:

- Real-time price charts
- News integration
- Push notifications
- Advanced portfolio analytics
- Social trading features

---

**Built with ❤️ using Flutter, Firebase, and Shorebird**

## Getting Started

This project is a starting point for a Flutter application.

A few resources to get you started if this is your first Flutter project:

- [Lab: Write your first Flutter app](https://docs.flutter.dev/get-started/codelab)
- [Cookbook: Useful Flutter samples](https://docs.flutter.dev/cookbook)

For help getting started with Flutter development, view the
[online documentation](https://docs.flutter.dev/), which offers tutorials,
samples, guidance on mobile development, and a full API reference.
