// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';

import 'package:shorebird_demo/main.dart';
import 'package:shorebird_demo/providers/stock_market_provider.dart';

void main() {
  testWidgets('Stock Market App smoke test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(
      ChangeNotifierProvider(
        create: (context) => StockMarketProvider(),
        child: const StockMarketApp(),
      ),
    );

    // Wait for initial frame
    await tester.pump();

    // Verify that the app loads with the main screen
    expect(find.text('Stock Market'), findsOneWidget);
  });
}
