import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../models/share.dart';
import '../models/user_portfolio.dart';
import '../providers/stock_market_provider.dart';
import '../services/stock_market_service.dart';

class BuySellDialog extends StatefulWidget {
  final Share share;
  final TransactionType transactionType;

  const BuySellDialog({
    super.key,
    required this.share,
    required this.transactionType,
  });

  @override
  State<BuySellDialog> createState() => _BuySellDialogState();
}

class _BuySellDialogState extends State<BuySellDialog> {
  final TextEditingController _quantityController = TextEditingController();
  bool _isProcessing = false;
  int _quantity = 1;

  @override
  void initState() {
    super.initState();
    _quantityController.text = '1';
  }

  @override
  void dispose() {
    _quantityController.dispose();
    super.dispose();
  }

  double get _totalAmount => _quantity * widget.share.currentPrice;
  double get _transactionFee => _totalAmount * 0.001; // 0.1% fee
  double get _netAmount => widget.transactionType == TransactionType.buy
      ? _totalAmount + _transactionFee
      : _totalAmount - _transactionFee;

  void _updateQuantity(String value) {
    final quantity = int.tryParse(value) ?? 1;
    setState(() {
      _quantity = quantity.clamp(1, 10000);
    });
  }

  Future<void> _executeTransaction() async {
    if (_isProcessing) return;

    setState(() {
      _isProcessing = true;
    });

    try {
      final provider = context.read<StockMarketProvider>();
      TransactionResult result;

      if (widget.transactionType == TransactionType.buy) {
        result = await provider.buyShares(widget.share.symbol, _quantity);
      } else {
        result = await provider.sellShares(widget.share.symbol, _quantity);
      }

      if (mounted) {
        Navigator.pop(context);

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(result.message),
            backgroundColor: result.success ? Colors.green : Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        Navigator.pop(context);

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Transaction failed: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<StockMarketProvider>(
      builder: (context, provider, child) {
        final isBuy = widget.transactionType == TransactionType.buy;
        final ownedQuantity = provider.portfolio.getOwnedQuantity(
          widget.share.symbol,
        );
        final maxSellQuantity = ownedQuantity;
        final canAfford = provider.portfolio.cashBalance >= _netAmount;

        return AlertDialog(
          title: Row(
            children: [
              Icon(
                isBuy ? Icons.add_shopping_cart : Icons.sell,
                color: isBuy ? Colors.green : Colors.red,
              ),
              const SizedBox(width: 8),
              Text('${isBuy ? 'Buy' : 'Sell'} ${widget.share.symbol}'),
            ],
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Share info
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.share.name,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Current Price: ₹${widget.share.currentPrice.toStringAsFixed(2)}',
                      ),
                      if (!isBuy && ownedQuantity > 0)
                        Text('You own: $ownedQuantity shares'),
                    ],
                  ),
                ),

                const SizedBox(height: 16),

                // Quantity input
                TextField(
                  controller: _quantityController,
                  keyboardType: TextInputType.number,
                  inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                  decoration: InputDecoration(
                    labelText: 'Quantity',
                    border: const OutlineInputBorder(),
                    suffixIcon: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        InkWell(
                          onTap: () {
                            final newQuantity = _quantity + 1;
                            if (!isBuy && newQuantity > maxSellQuantity) return;
                            _quantityController.text = newQuantity.toString();
                            _updateQuantity(newQuantity.toString());
                          },
                          child: const Icon(Icons.keyboard_arrow_up),
                        ),
                        InkWell(
                          onTap: () {
                            final newQuantity = (_quantity - 1).clamp(1, 10000);
                            _quantityController.text = newQuantity.toString();
                            _updateQuantity(newQuantity.toString());
                          },
                          child: const Icon(Icons.keyboard_arrow_down),
                        ),
                      ],
                    ),
                  ),
                  onChanged: _updateQuantity,
                ),

                const SizedBox(height: 16),

                // Transaction summary
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: isBuy ? Colors.green.shade50 : Colors.red.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: isBuy
                          ? Colors.green.shade200
                          : Colors.red.shade200,
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text('Total Amount:'),
                          Text('₹${_totalAmount.toStringAsFixed(2)}'),
                        ],
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text('Transaction Fee:'),
                          Text('₹${_transactionFee.toStringAsFixed(2)}'),
                        ],
                      ),
                      const Divider(),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            isBuy ? 'Total Cost:' : 'You\'ll Receive:',
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                          Text(
                            '₹${_netAmount.toStringAsFixed(2)}',
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // Validation messages
                if (isBuy && !canAfford) ...[
                  const SizedBox(height: 8),
                  Text(
                    'Insufficient cash balance',
                    style: TextStyle(color: Colors.red.shade700),
                  ),
                ],

                if (!isBuy && _quantity > maxSellQuantity) ...[
                  const SizedBox(height: 8),
                  Text(
                    'You can sell maximum $maxSellQuantity shares',
                    style: TextStyle(color: Colors.red.shade700),
                  ),
                ],
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: _isProcessing ? null : () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed:
                  _isProcessing ||
                      (isBuy && !canAfford) ||
                      (!isBuy && _quantity > maxSellQuantity)
                  ? null
                  : _executeTransaction,
              style: ElevatedButton.styleFrom(
                backgroundColor: isBuy ? Colors.green : Colors.red,
                foregroundColor: Colors.white,
              ),
              child: _isProcessing
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Text(isBuy ? 'Buy' : 'Sell'),
            ),
          ],
        );
      },
    );
  }
}
