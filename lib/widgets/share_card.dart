import 'package:flutter/material.dart';
import '../models/share.dart';

class ShareCard extends StatelessWidget {
  final Share share;
  final int ownedQuantity;
  final VoidCallback? onBuy;
  final VoidCallback? onSell;
  final bool showMarketTrend;

  const ShareCard({
    super.key,
    required this.share,
    required this.ownedQuantity,
    this.onBuy,
    this.onSell,
    this.showMarketTrend = false,
  });

  String _getMarketTrend() {
    final changePercent = share.priceChangePercentage;
    if (changePercent > 5) return 'Strong Bull';
    if (changePercent > 2) return 'Bull';
    if (changePercent > -2) return 'Neutral';
    if (changePercent > -5) return 'Bear';
    return 'Strong Bear';
  }

  Color _getTrendColor() {
    final changePercent = share.priceChangePercentage;
    if (changePercent > 2) return Colors.green;
    if (changePercent > -2) return Colors.orange;
    return Colors.red;
  }

  @override
  Widget build(BuildContext context) {
    final isPositive = share.isPriceUp;
    final changeColor = isPositive ? Colors.green : Colors.red;
    final changeIcon = isPositive ? Icons.trending_up : Icons.trending_down;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header row
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        share.symbol,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        share.name,
                        style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      Text(
                        share.sector,
                        style: TextStyle(fontSize: 12, color: Colors.grey[500]),
                      ),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      '₹${share.currentPrice.toStringAsFixed(2)}',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(changeIcon, color: changeColor, size: 16),
                        const SizedBox(width: 4),
                        Text(
                          '${share.priceChangePercentage >= 0 ? '+' : ''}${share.priceChangePercentage.toStringAsFixed(2)}%',
                          style: TextStyle(
                            color: changeColor,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                    Text(
                      '₹${share.priceChange >= 0 ? '+' : ''}${share.priceChange.toStringAsFixed(2)}',
                      style: TextStyle(color: changeColor, fontSize: 12),
                    ),
                  ],
                ),
              ],
            ),

            // Market trend (if enabled)
            if (showMarketTrend) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getTrendColor().withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: _getTrendColor().withValues(alpha: 0.3),
                  ),
                ),
                child: Text(
                  'Trend: ${_getMarketTrend()}',
                  style: TextStyle(
                    color: _getTrendColor(),
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],

            // Owned quantity (if any)
            if (ownedQuantity > 0) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: Row(
                  children: [
                    const Icon(
                      Icons.account_balance_wallet,
                      color: Colors.blue,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'You own: $ownedQuantity shares',
                      style: const TextStyle(
                        color: Colors.blue,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const Spacer(),
                    Text(
                      'Value: ₹${(ownedQuantity * share.currentPrice).toStringAsFixed(2)}',
                      style: const TextStyle(
                        color: Colors.blue,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ],

            const SizedBox(height: 16),

            // Action buttons
            if (onBuy != null || onSell != null)
              Row(
                children: [
                  if (onBuy != null)
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: onBuy,
                        icon: const Icon(Icons.add_shopping_cart),
                        label: const Text('Buy'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                  if (onBuy != null && onSell != null)
                    const SizedBox(width: 12),
                  if (onSell != null)
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: onSell,
                        icon: const Icon(Icons.sell),
                        label: const Text('Sell'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                ],
              )
            else
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: const Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.lock, color: Colors.grey, size: 20),
                    SizedBox(width: 8),
                    Text(
                      'Market Closed - Trading Disabled',
                      style: TextStyle(
                        color: Colors.grey,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }
}
