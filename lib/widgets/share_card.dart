import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../models/share.dart';

class ShareCard extends StatelessWidget {
  final Share share;
  final int ownedQuantity;
  final VoidCallback? onBuy;
  final VoidCallback? onSell;
  final bool showMarketTrend;

  const ShareCard({
    super.key,
    required this.share,
    required this.ownedQuantity,
    this.onBuy,
    this.onSell,
    this.showMarketTrend = false,
  });

  // Vintage color palette
  static const Color _vintageGold = Color(0xFFD4AF37);
  static const Color _vintageBrown = Color(0xFF8B4513);
  static const Color _vintageBeige = Color(0xFFF5F5DC);
  static const Color _vintageMaroon = Color(0xFF800020);
  static const Color _vintageOlive = Color(0xFF6B8E23);
  static const Color _vintageTeal = Color(0xFF008B8B);
  static const Color _vintageRust = Color(0xFFB7410E);
  static const Color _vintageIvory = Color(0xFFFFFFF0);
  static const Color _vintageBronze = Color(0xFFCD7F32);

  String _getMarketTrend() {
    final changePercent = share.priceChangePercentage;
    if (changePercent > 5) return 'STRONG BULL';
    if (changePercent > 2) return 'BULLISH';
    if (changePercent > -2) return 'STABLE';
    if (changePercent > -5) return 'BEARISH';
    return 'STRONG BEAR';
  }

  Color _getTrendColor() {
    final changePercent = share.priceChangePercentage;
    if (changePercent > 2) return _vintageOlive;
    if (changePercent > -2) return _vintageGold;
    return _vintageRust;
  }

  IconData _getTrendIcon() {
    final changePercent = share.priceChangePercentage;
    if (changePercent > 2) return Icons.trending_up_rounded;
    if (changePercent > -2) return Icons.trending_flat_rounded;
    return Icons.trending_down_rounded;
  }

  @override
  Widget build(BuildContext context) {
    final isPositive = share.isPriceUp;
    final changeColor = isPositive ? _vintageOlive : _vintageRust;
    final changeIcon = isPositive
        ? Icons.trending_up_rounded
        : Icons.trending_down_rounded;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            _vintageIvory,
            _vintageBeige.withValues(alpha: 0.9),
            _vintageBeige.withValues(alpha: 0.7),
          ],
          stops: const [0.0, 0.5, 1.0],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: _vintageGold, width: 2),
        boxShadow: [
          BoxShadow(
            color: _vintageBrown.withValues(alpha: 0.2),
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
          BoxShadow(
            color: _vintageGold.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 25,
            offset: const Offset(0, 15),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(18),
        child: Container(
          decoration: BoxDecoration(
            border: Border(
              top: BorderSide(
                color: _vintageGold.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Premium Header Section
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        _vintageGold.withValues(alpha: 0.1),
                        _vintageGold.withValues(alpha: 0.05),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(15),
                    border: Border.all(
                      color: _vintageGold.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      // Company Info Section
                      Expanded(
                        flex: 3,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Stock Symbol with Icon
                            Row(
                              children: [
                                Container(
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      colors: [
                                        _vintageBrown,
                                        _vintageBrown.withValues(alpha: 0.8),
                                      ],
                                    ),
                                    borderRadius: BorderRadius.circular(10),
                                    boxShadow: [
                                      BoxShadow(
                                        color: _vintageBrown.withValues(
                                          alpha: 0.3,
                                        ),
                                        blurRadius: 4,
                                        offset: const Offset(0, 2),
                                      ),
                                    ],
                                  ),
                                  child: Icon(
                                    Icons.business_center_rounded,
                                    color: _vintageGold,
                                    size: 16,
                                  ),
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        share.symbol,
                                        style: GoogleFonts.playfairDisplay(
                                          fontSize: 20,
                                          fontWeight: FontWeight.bold,
                                          color: _vintageBrown,
                                          letterSpacing: 0.5,
                                        ),
                                      ),
                                      Text(
                                        share.name,
                                        style: GoogleFonts.roboto(
                                          fontSize: 13,
                                          color: _vintageBrown.withValues(
                                            alpha: 0.8,
                                          ),
                                          fontWeight: FontWeight.w500,
                                        ),
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            // Sector Badge
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 10,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: _vintageTeal.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: _vintageTeal.withValues(alpha: 0.3),
                                  width: 1,
                                ),
                              ),
                              child: Text(
                                share.sector.toUpperCase(),
                                style: GoogleFonts.robotoMono(
                                  fontSize: 10,
                                  color: _vintageTeal,
                                  fontWeight: FontWeight.bold,
                                  letterSpacing: 0.8,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Price Section
                      Expanded(
                        flex: 2,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            // Current Price
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 8,
                              ),
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: [
                                    _vintageMaroon.withValues(alpha: 0.1),
                                    _vintageMaroon.withValues(alpha: 0.05),
                                  ],
                                ),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: _vintageMaroon.withValues(alpha: 0.2),
                                  width: 1,
                                ),
                              ),
                              child: Text(
                                '₹${share.currentPrice.toStringAsFixed(2)}',
                                style: GoogleFonts.playfairDisplay(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: _vintageBrown,
                                  letterSpacing: 0.3,
                                ),
                              ),
                            ),
                            const SizedBox(height: 8),
                            // Change Indicator
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 10,
                                vertical: 6,
                              ),
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: [
                                    changeColor.withValues(alpha: 0.15),
                                    changeColor.withValues(alpha: 0.08),
                                  ],
                                ),
                                borderRadius: BorderRadius.circular(20),
                                border: Border.all(
                                  color: changeColor.withValues(alpha: 0.4),
                                  width: 1.5,
                                ),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    changeIcon,
                                    color: changeColor,
                                    size: 14,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    '${share.priceChangePercentage >= 0 ? '+' : ''}${share.priceChangePercentage.toStringAsFixed(2)}%',
                                    style: GoogleFonts.robotoMono(
                                      color: changeColor,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 12,
                                      letterSpacing: 0.3,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(height: 4),
                            // Price Change Amount
                            Text(
                              '₹${share.priceChange >= 0 ? '+' : ''}${share.priceChange.toStringAsFixed(2)}',
                              style: GoogleFonts.roboto(
                                color: changeColor.withValues(alpha: 0.8),
                                fontSize: 11,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 16),

                // Market Trend Section (if enabled)
                if (showMarketTrend) ...[
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(14),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.centerLeft,
                        end: Alignment.centerRight,
                        colors: [
                          _getTrendColor().withValues(alpha: 0.1),
                          _getTrendColor().withValues(alpha: 0.05),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(15),
                      border: Border.all(
                        color: _getTrendColor().withValues(alpha: 0.4),
                        width: 1.5,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: _getTrendColor().withValues(alpha: 0.2),
                          blurRadius: 6,
                          offset: const Offset(0, 3),
                        ),
                      ],
                    ),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: _getTrendColor().withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Icon(
                            _getTrendIcon(),
                            color: _getTrendColor(),
                            size: 18,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'MARKET TREND',
                                style: GoogleFonts.robotoMono(
                                  fontSize: 10,
                                  color: _getTrendColor().withValues(
                                    alpha: 0.8,
                                  ),
                                  fontWeight: FontWeight.w600,
                                  letterSpacing: 1.2,
                                ),
                              ),
                              const SizedBox(height: 2),
                              Text(
                                _getMarketTrend(),
                                style: GoogleFonts.playfairDisplay(
                                  color: _getTrendColor(),
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  letterSpacing: 0.5,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),
                ],

                // Portfolio Holdings Section (if any)
                if (ownedQuantity > 0) ...[
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          _vintageBronze.withValues(alpha: 0.1),
                          _vintageBronze.withValues(alpha: 0.05),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(15),
                      border: Border.all(
                        color: _vintageBronze.withValues(alpha: 0.3),
                        width: 1.5,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: _vintageBronze.withValues(alpha: 0.2),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(10),
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: [
                                    _vintageBronze,
                                    _vintageBronze.withValues(alpha: 0.8),
                                  ],
                                ),
                                borderRadius: BorderRadius.circular(12),
                                boxShadow: [
                                  BoxShadow(
                                    color: _vintageBronze.withValues(
                                      alpha: 0.3,
                                    ),
                                    blurRadius: 4,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Icon(
                                Icons.account_balance_wallet_rounded,
                                color: _vintageIvory,
                                size: 20,
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'PORTFOLIO HOLDINGS',
                                    style: GoogleFonts.robotoMono(
                                      fontSize: 10,
                                      color: _vintageBronze.withValues(
                                        alpha: 0.8,
                                      ),
                                      fontWeight: FontWeight.w600,
                                      letterSpacing: 1.0,
                                    ),
                                  ),
                                  const SizedBox(height: 2),
                                  Text(
                                    '$ownedQuantity Shares Owned',
                                    style: GoogleFonts.playfairDisplay(
                                      color: _vintageBronze,
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: _vintageIvory.withValues(alpha: 0.5),
                            borderRadius: BorderRadius.circular(10),
                            border: Border.all(
                              color: _vintageBronze.withValues(alpha: 0.2),
                              width: 1,
                            ),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Total Investment Value:',
                                style: GoogleFonts.roboto(
                                  color: _vintageBrown,
                                  fontSize: 13,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              Text(
                                '₹${(ownedQuantity * share.currentPrice).toStringAsFixed(2)}',
                                style: GoogleFonts.playfairDisplay(
                                  color: _vintageBronze,
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  letterSpacing: 0.3,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),
                ],

                // Premium Action Buttons Section
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        _vintageGold.withValues(alpha: 0.08),
                        _vintageGold.withValues(alpha: 0.03),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(15),
                    border: Border.all(
                      color: _vintageGold.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: onBuy != null || onSell != null
                      ? Row(
                          children: [
                            if (onBuy != null)
                              Expanded(
                                child: Container(
                                  height: 50,
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                      colors: [
                                        _vintageOlive,
                                        _vintageOlive.withValues(alpha: 0.8),
                                      ],
                                    ),
                                    borderRadius: BorderRadius.circular(12),
                                    border: Border.all(
                                      color: _vintageGold.withValues(
                                        alpha: 0.5,
                                      ),
                                      width: 1,
                                    ),
                                    boxShadow: [
                                      BoxShadow(
                                        color: _vintageOlive.withValues(
                                          alpha: 0.3,
                                        ),
                                        blurRadius: 6,
                                        offset: const Offset(0, 3),
                                      ),
                                    ],
                                  ),
                                  child: ElevatedButton.icon(
                                    onPressed: onBuy,
                                    icon: Icon(
                                      Icons.add_shopping_cart_rounded,
                                      color: _vintageIvory,
                                      size: 18,
                                    ),
                                    label: Text(
                                      'BUY SHARES',
                                      style: GoogleFonts.robotoMono(
                                        color: _vintageIvory,
                                        fontWeight: FontWeight.bold,
                                        fontSize: 12,
                                        letterSpacing: 1.0,
                                      ),
                                    ),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.transparent,
                                      shadowColor: Colors.transparent,
                                      elevation: 0,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            if (onBuy != null && onSell != null)
                              const SizedBox(width: 12),
                            if (onSell != null)
                              Expanded(
                                child: Container(
                                  height: 50,
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                      colors: [
                                        _vintageRust,
                                        _vintageRust.withValues(alpha: 0.8),
                                      ],
                                    ),
                                    borderRadius: BorderRadius.circular(12),
                                    border: Border.all(
                                      color: _vintageGold.withValues(
                                        alpha: 0.5,
                                      ),
                                      width: 1,
                                    ),
                                    boxShadow: [
                                      BoxShadow(
                                        color: _vintageRust.withValues(
                                          alpha: 0.3,
                                        ),
                                        blurRadius: 6,
                                        offset: const Offset(0, 3),
                                      ),
                                    ],
                                  ),
                                  child: ElevatedButton.icon(
                                    onPressed: onSell,
                                    icon: Icon(
                                      Icons.sell_rounded,
                                      color: _vintageIvory,
                                      size: 18,
                                    ),
                                    label: Text(
                                      'SELL SHARES',
                                      style: GoogleFonts.robotoMono(
                                        color: _vintageIvory,
                                        fontWeight: FontWeight.bold,
                                        fontSize: 12,
                                        letterSpacing: 1.0,
                                      ),
                                    ),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.transparent,
                                      shadowColor: Colors.transparent,
                                      elevation: 0,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                          ],
                        )
                      : Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                _vintageBrown.withValues(alpha: 0.1),
                                _vintageBrown.withValues(alpha: 0.05),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: _vintageBrown.withValues(alpha: 0.3),
                              width: 1,
                            ),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.lock_rounded,
                                color: _vintageBrown.withValues(alpha: 0.7),
                                size: 20,
                              ),
                              const SizedBox(width: 12),
                              Text(
                                'MARKET CLOSED - TRADING SUSPENDED',
                                style: GoogleFonts.robotoMono(
                                  color: _vintageBrown.withValues(alpha: 0.8),
                                  fontWeight: FontWeight.bold,
                                  fontSize: 11,
                                  letterSpacing: 0.8,
                                ),
                              ),
                            ],
                          ),
                        ),
                ),
              ],
            ),
          ),
        ),
      ),
    ).animate().fadeIn(duration: 600.ms).slideY(begin: 0.3, end: 0);
  }
}
