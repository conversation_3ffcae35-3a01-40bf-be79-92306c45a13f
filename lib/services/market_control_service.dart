import 'dart:developer';
import 'local_storage_service.dart';
import 'firebase_remote_config_service.dart';

class MarketControlService {
  static final MarketControlService _instance = MarketControlService._internal();
  factory MarketControlService() => _instance;
  MarketControlService._internal();

  final LocalStorageService _localStorage = LocalStorageService();
  final FirebaseRemoteConfigService _remoteConfig = FirebaseRemoteConfigService();
  
  static const String _marketStatusKey = 'local_market_status';
  
  bool _localMarketOpen = true;
  
  // Callback for when market status changes
  Function(bool isOpen)? onMarketStatusChanged;

  // Initialize the service
  Future<void> initialize() async {
    try {
      await _localStorage.initialize();
      
      // Load saved market status (defaults to open)
      _localMarketOpen = await _localStorage.loadSetting<bool>(
        _marketStatusKey, 
        defaultValue: true,
      ) ?? true;
      
      log('Market control service initialized. Market is ${_localMarketOpen ? "OPEN" : "CLOSED"}');
    } catch (e) {
      log('Error initializing market control service: $e');
      _localMarketOpen = true; // Default to open on error
    }
  }

  // Get current market status (combines local and remote)
  bool get isMarketOpen {
    // Check remote config first
    final remoteStatus = _remoteConfig.marketStatus;
    if (remoteStatus == 'CLOSED' || _remoteConfig.isMaintenanceMode) {
      return false;
    }
    
    // Then check local override
    return _localMarketOpen;
  }

  // Get local market status only
  bool get isLocalMarketOpen => _localMarketOpen;

  // Get remote market status
  String get remoteMarketStatus => _remoteConfig.marketStatus;

  // Open the market locally
  Future<void> openMarket() async {
    try {
      _localMarketOpen = true;
      await _localStorage.saveSetting(_marketStatusKey, true);
      onMarketStatusChanged?.call(true);
      log('Market opened locally');
    } catch (e) {
      log('Error opening market: $e');
    }
  }

  // Close the market locally
  Future<void> closeMarket() async {
    try {
      _localMarketOpen = false;
      await _localStorage.saveSetting(_marketStatusKey, false);
      onMarketStatusChanged?.call(false);
      log('Market closed locally');
    } catch (e) {
      log('Error closing market: $e');
    }
  }

  // Toggle market status
  Future<void> toggleMarket() async {
    if (_localMarketOpen) {
      await closeMarket();
    } else {
      await openMarket();
    }
  }

  // Get market status info
  Map<String, dynamic> getMarketInfo() {
    return {
      'isOpen': isMarketOpen,
      'localStatus': _localMarketOpen ? 'OPEN' : 'CLOSED',
      'remoteStatus': remoteMarketStatus,
      'maintenanceMode': _remoteConfig.isMaintenanceMode,
      'canTrade': isMarketOpen && !_remoteConfig.isMaintenanceMode,
    };
  }

  // Get market status display text
  String get marketStatusText {
    if (_remoteConfig.isMaintenanceMode) {
      return 'MAINTENANCE';
    }
    
    if (remoteMarketStatus == 'CLOSED') {
      return 'CLOSED (Remote)';
    }
    
    return _localMarketOpen ? 'OPEN' : 'CLOSED';
  }

  // Get market status color
  MarketStatusColor get marketStatusColor {
    if (_remoteConfig.isMaintenanceMode) {
      return MarketStatusColor.maintenance;
    }
    
    if (!isMarketOpen) {
      return MarketStatusColor.closed;
    }
    
    return MarketStatusColor.open;
  }
}

enum MarketStatusColor {
  open,
  closed,
  maintenance,
}

extension MarketStatusColorExtension on MarketStatusColor {
  String get colorName {
    switch (this) {
      case MarketStatusColor.open:
        return 'green';
      case MarketStatusColor.closed:
        return 'red';
      case MarketStatusColor.maintenance:
        return 'orange';
    }
  }
}
