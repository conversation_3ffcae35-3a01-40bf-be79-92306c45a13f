import 'dart:async';
import 'dart:developer';
import 'dart:math' as math;
import '../models/share.dart';
import 'firebase_remote_config_service.dart';

class PriceFluctuationService {
  static final PriceFluctuationService _instance = PriceFluctuationService._internal();
  factory PriceFluctuationService() => _instance;
  PriceFluctuationService._internal();

  final FirebaseRemoteConfigService _remoteConfig = FirebaseRemoteConfigService();
  Timer? _fluctuationTimer;
  final math.Random _random = math.Random();
  
  // Callback for when prices change
  Function(Map<String, Share>)? onPricesUpdated;

  // Start automatic price fluctuation
  void startPriceFluctuation(Map<String, Share> initialShares) {
    _stopFluctuation();
    
    if (!_remoteConfig.isAutoPriceFluctuationEnabled) {
      log('Auto price fluctuation is disabled');
      return;
    }

    if (_remoteConfig.marketStatus != 'OPEN') {
      log('Market is closed, price fluctuation stopped');
      return;
    }

    final interval = Duration(seconds: _remoteConfig.priceFluctuationInterval);
    log('Starting price fluctuation with ${interval.inSeconds}s interval');

    _fluctuationTimer = Timer.periodic(interval, (timer) {
      if (_remoteConfig.marketStatus != 'OPEN' || !_remoteConfig.isAutoPriceFluctuationEnabled) {
        log('Market closed or fluctuation disabled, stopping timer');
        _stopFluctuation();
        return;
      }

      final updatedShares = _fluctuatePrices(initialShares);
      onPricesUpdated?.call(updatedShares);
    });
  }

  // Stop price fluctuation
  void _stopFluctuation() {
    _fluctuationTimer?.cancel();
    _fluctuationTimer = null;
  }

  // Stop fluctuation (public method)
  void stopPriceFluctuation() {
    _stopFluctuation();
    log('Price fluctuation stopped');
  }

  // Fluctuate prices for all shares
  Map<String, Share> _fluctuatePrices(Map<String, Share> currentShares) {
    final updatedShares = <String, Share>{};
    final maxChangePercent = _remoteConfig.maxPriceChangePercent;

    for (final entry in currentShares.entries) {
      final symbol = entry.key;
      final currentShare = entry.value;
      
      // Generate random price change between -maxChangePercent and +maxChangePercent
      final changePercent = (_random.nextDouble() * 2 - 1) * maxChangePercent;
      final priceChange = currentShare.currentPrice * (changePercent / 100);
      final newPrice = math.max(1.0, currentShare.currentPrice + priceChange); // Minimum price of ₹1

      // Create updated share with new price
      updatedShares[symbol] = currentShare.copyWith(
        previousPrice: currentShare.currentPrice,
        currentPrice: double.parse(newPrice.toStringAsFixed(2)),
      );

      log('$symbol: ₹${currentShare.currentPrice.toStringAsFixed(2)} → ₹${newPrice.toStringAsFixed(2)} (${changePercent.toStringAsFixed(2)}%)');
    }

    return updatedShares;
  }

  // Manual price fluctuation (for testing)
  Map<String, Share> fluctuatePricesOnce(Map<String, Share> currentShares) {
    return _fluctuatePrices(currentShares);
  }

  // Get fluctuation status
  bool get isFluctuationActive => _fluctuationTimer?.isActive ?? false;

  // Get current settings
  Map<String, dynamic> getFluctuationSettings() {
    return {
      'enabled': _remoteConfig.isAutoPriceFluctuationEnabled,
      'interval': _remoteConfig.priceFluctuationInterval,
      'maxChangePercent': _remoteConfig.maxPriceChangePercent,
      'marketStatus': _remoteConfig.marketStatus,
      'isActive': isFluctuationActive,
    };
  }

  // Simulate market events (for demo purposes)
  Map<String, Share> simulateMarketEvent(Map<String, Share> currentShares, MarketEvent event) {
    final updatedShares = <String, Share>{};
    
    for (final entry in currentShares.entries) {
      final symbol = entry.key;
      final currentShare = entry.value;
      double changePercent = 0.0;

      switch (event) {
        case MarketEvent.bullRun:
          changePercent = 2.0 + _random.nextDouble() * 8.0; // +2% to +10%
          break;
        case MarketEvent.bearMarket:
          changePercent = -2.0 - _random.nextDouble() * 8.0; // -2% to -10%
          break;
        case MarketEvent.volatility:
          changePercent = (_random.nextDouble() * 2 - 1) * 15.0; // -15% to +15%
          break;
        case MarketEvent.stability:
          changePercent = (_random.nextDouble() * 2 - 1) * 2.0; // -2% to +2%
          break;
      }

      final priceChange = currentShare.currentPrice * (changePercent / 100);
      final newPrice = math.max(1.0, currentShare.currentPrice + priceChange);

      updatedShares[symbol] = currentShare.copyWith(
        previousPrice: currentShare.currentPrice,
        currentPrice: double.parse(newPrice.toStringAsFixed(2)),
      );
    }

    log('Market event ${event.name} applied to all shares');
    return updatedShares;
  }

  // Dispose resources
  void dispose() {
    _stopFluctuation();
  }
}

enum MarketEvent {
  bullRun,
  bearMarket,
  volatility,
  stability,
}

extension MarketEventExtension on MarketEvent {
  String get name {
    switch (this) {
      case MarketEvent.bullRun:
        return 'Bull Run';
      case MarketEvent.bearMarket:
        return 'Bear Market';
      case MarketEvent.volatility:
        return 'High Volatility';
      case MarketEvent.stability:
        return 'Market Stability';
    }
  }

  String get description {
    switch (this) {
      case MarketEvent.bullRun:
        return 'Prices trending upward (+2% to +10%)';
      case MarketEvent.bearMarket:
        return 'Prices trending downward (-2% to -10%)';
      case MarketEvent.volatility:
        return 'High price swings (-15% to +15%)';
      case MarketEvent.stability:
        return 'Minimal price changes (-2% to +2%)';
    }
  }
}
