import 'dart:developer';
import 'dart:math' as math;
import '../models/share.dart';
import '../models/user_portfolio.dart';
import 'firebase_remote_config_service.dart';
import 'local_storage_service.dart';
import 'price_fluctuation_service.dart';
import 'market_control_service.dart';

class StockMarketService {
  static final StockMarketService _instance = StockMarketService._internal();
  factory StockMarketService() => _instance;
  StockMarketService._internal();

  final FirebaseRemoteConfigService _remoteConfig =
      FirebaseRemoteConfigService();
  final LocalStorageService _localStorage = LocalStorageService();
  final PriceFluctuationService _priceFluctuation = PriceFluctuationService();
  final MarketControlService _marketControl = MarketControlService();

  // Predefined shares data
  static const Map<String, Map<String, String>> _shareData = {
    'TATA': {'name': 'Tata Motors Ltd.', 'sector': 'Automotive'},
    'RELIANCE': {'name': 'Reliance Industries Ltd.', 'sector': 'Oil & Gas'},
    'INFY': {'name': 'Infosys Ltd.', 'sector': 'IT Services'},
    'TCS': {'name': 'Tata Consultancy Services', 'sector': 'IT Services'},
    'HDFC': {'name': 'HDFC Bank Ltd.', 'sector': 'Banking'},
    'ICICI': {'name': 'ICICI Bank Ltd.', 'sector': 'Banking'},
    'WIPRO': {'name': 'Wipro Ltd.', 'sector': 'IT Services'},
    'BHARTI': {'name': 'Bharti Airtel Ltd.', 'sector': 'Telecom'},
    'ITC': {'name': 'ITC Ltd.', 'sector': 'FMCG'},
    'SBIN': {'name': 'State Bank of India', 'sector': 'Banking'},
  };

  Map<String, Share> _shares = {};
  UserPortfolio _portfolio = UserPortfolio();

  // Callback for when prices are updated
  Function(Map<String, Share>)? onPricesUpdated;

  // Initialize the service
  Future<void> initialize() async {
    try {
      await _localStorage.initialize();
      await _remoteConfig.initialize();
      await _marketControl.initialize();

      // Load cached data first
      await _loadCachedData();

      // Update with latest prices
      await updateSharePrices();

      // Setup price fluctuation callback
      _priceFluctuation.onPricesUpdated = (updatedShares) {
        _shares = updatedShares;
        onPricesUpdated?.call(_shares);
      };

      // Start automatic price fluctuation
      _priceFluctuation.startPriceFluctuation(_shares);

      log('Stock market service initialized successfully');
    } catch (e) {
      log('Error initializing stock market service: $e');
    }
  }

  // Load cached data from local storage
  Future<void> _loadCachedData() async {
    try {
      // Load portfolio
      _portfolio = await _localStorage.loadPortfolio();

      // Load shares or create default ones
      final cachedShares = await _localStorage.loadShares();
      if (cachedShares != null) {
        _shares = cachedShares;
      } else {
        _createDefaultShares();
      }
    } catch (e) {
      log('Error loading cached data: $e');
      _createDefaultShares();
    }
  }

  // Create default shares with initial prices
  void _createDefaultShares() {
    _shares.clear();
    final random = math.Random();

    _shareData.forEach((symbol, data) {
      final basePrice = _remoteConfig.getSharePrice(symbol);
      final previousPrice =
          basePrice * (0.95 + random.nextDouble() * 0.1); // ±5% variation

      _shares[symbol] = Share(
        symbol: symbol,
        name: data['name']!,
        currentPrice: basePrice,
        previousPrice: previousPrice,
        sector: data['sector']!,
      );
    });
  }

  // Update share prices from remote config
  Future<void> updateSharePrices() async {
    try {
      await _remoteConfig.fetchAndActivate();

      final updatedShares = <String, Share>{};

      _shareData.forEach((symbol, data) {
        final currentShare = _shares[symbol];
        final newPrice = _remoteConfig.getSharePrice(symbol);

        updatedShares[symbol] = Share(
          symbol: symbol,
          name: data['name']!,
          currentPrice: newPrice,
          previousPrice: currentShare?.currentPrice ?? newPrice,
          sector: data['sector']!,
        );
      });

      _shares = updatedShares;

      // Cache the updated shares
      await _localStorage.saveShares(_shares);

      log('Share prices updated successfully');
    } catch (e) {
      log('Error updating share prices: $e');
    }
  }

  // Get all shares
  Map<String, Share> get shares => Map.unmodifiable(_shares);

  // Get user portfolio
  UserPortfolio get portfolio => _portfolio;

  // Buy shares
  Future<TransactionResult> buyShares(String symbol, int quantity) async {
    try {
      // Check if market is open
      if (!_marketControl.isMarketOpen) {
        return TransactionResult(
          success: false,
          message: 'Market is closed. Trading is not allowed.',
        );
      }

      final share = _shares[symbol];
      if (share == null) {
        return TransactionResult(success: false, message: 'Share not found');
      }

      final totalCost = share.currentPrice * quantity;
      final fee = totalCost * (_remoteConfig.transactionFeePercentage / 100);
      final totalAmount = totalCost + fee;

      // Check if user has enough cash
      if (_portfolio.cashBalance < totalAmount) {
        return TransactionResult(
          success: false,
          message: 'Insufficient cash balance',
        );
      }

      // Check transaction limits
      if (totalCost < _remoteConfig.minTransactionAmount) {
        return TransactionResult(
          success: false,
          message: 'Transaction amount below minimum limit',
        );
      }

      if (totalCost > _remoteConfig.maxTransactionAmount) {
        return TransactionResult(
          success: false,
          message: 'Transaction amount exceeds maximum limit',
        );
      }

      // Create transaction
      final transaction = Transaction(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        symbol: symbol,
        type: TransactionType.buy,
        quantity: quantity,
        price: share.currentPrice,
        timestamp: DateTime.now(),
      );

      // Update portfolio
      final existingHolding = _portfolio.holdings[symbol];
      final newHoldings = Map<String, ShareHolding>.from(_portfolio.holdings);

      if (existingHolding != null) {
        // Update existing holding with average price
        final totalQuantity = existingHolding.quantity + quantity;
        final totalValue =
            (existingHolding.quantity * existingHolding.averagePrice) +
            totalCost;
        final newAveragePrice = totalValue / totalQuantity;

        newHoldings[symbol] = existingHolding.copyWith(
          quantity: totalQuantity,
          averagePrice: newAveragePrice,
          lastTransactionDate: DateTime.now(),
        );
      } else {
        // Create new holding
        newHoldings[symbol] = ShareHolding(
          symbol: symbol,
          quantity: quantity,
          averagePrice: share.currentPrice,
          lastTransactionDate: DateTime.now(),
        );
      }

      // Update portfolio
      _portfolio = _portfolio.copyWith(
        holdings: newHoldings,
        transactions: [..._portfolio.transactions, transaction],
        cashBalance: _portfolio.cashBalance - totalAmount,
      );

      // Save to local storage
      await _localStorage.savePortfolio(_portfolio);

      return TransactionResult(
        success: true,
        message: 'Successfully bought $quantity shares of $symbol',
        transaction: transaction,
      );
    } catch (e) {
      log('Error buying shares: $e');
      return TransactionResult(
        success: false,
        message: 'Transaction failed: ${e.toString()}',
      );
    }
  }

  // Sell shares
  Future<TransactionResult> sellShares(String symbol, int quantity) async {
    try {
      // Check if market is open
      if (!_marketControl.isMarketOpen) {
        return TransactionResult(
          success: false,
          message: 'Market is closed. Trading is not allowed.',
        );
      }

      // Check if sell option is enabled
      if (!_remoteConfig.isSellOptionEnabled) {
        return TransactionResult(
          success: false,
          message: 'Sell option is currently disabled',
        );
      }

      final share = _shares[symbol];
      if (share == null) {
        return TransactionResult(success: false, message: 'Share not found');
      }

      final holding = _portfolio.holdings[symbol];
      if (holding == null || holding.quantity < quantity) {
        return TransactionResult(
          success: false,
          message: 'Insufficient shares to sell',
        );
      }

      final totalValue = share.currentPrice * quantity;
      final fee = totalValue * (_remoteConfig.transactionFeePercentage / 100);
      final netAmount = totalValue - fee;

      // Create transaction
      final transaction = Transaction(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        symbol: symbol,
        type: TransactionType.sell,
        quantity: quantity,
        price: share.currentPrice,
        timestamp: DateTime.now(),
      );

      // Update portfolio
      final newHoldings = Map<String, ShareHolding>.from(_portfolio.holdings);

      if (holding.quantity == quantity) {
        // Remove holding completely
        newHoldings.remove(symbol);
      } else {
        // Update holding quantity
        newHoldings[symbol] = holding.copyWith(
          quantity: holding.quantity - quantity,
          lastTransactionDate: DateTime.now(),
        );
      }

      // Update portfolio
      _portfolio = _portfolio.copyWith(
        holdings: newHoldings,
        transactions: [..._portfolio.transactions, transaction],
        cashBalance: _portfolio.cashBalance + netAmount,
      );

      // Save to local storage
      await _localStorage.savePortfolio(_portfolio);

      return TransactionResult(
        success: true,
        message: 'Successfully sold $quantity shares of $symbol',
        transaction: transaction,
      );
    } catch (e) {
      log('Error selling shares: $e');
      return TransactionResult(
        success: false,
        message: 'Transaction failed: ${e.toString()}',
      );
    }
  }

  // Get feature flags
  bool get isSellOptionEnabled => _remoteConfig.isSellOptionEnabled;
  bool get isSortingEnabled => _remoteConfig.isSortingEnabled;
  bool get isMarketTrendEnabled => _remoteConfig.isMarketTrendEnabled;
  String get marketStatus => _marketControl.marketStatusText;
  bool get isMaintenanceMode => _remoteConfig.isMaintenanceMode;
  bool get isMarketOpen => _marketControl.isMarketOpen;

  // Reset portfolio (for testing)
  Future<void> resetPortfolio() async {
    _portfolio = UserPortfolio();
    await _localStorage.savePortfolio(_portfolio);
  }

  // Market control methods
  void startPriceFluctuation() {
    _priceFluctuation.startPriceFluctuation(_shares);
    log('Price fluctuation started manually');
  }

  void stopPriceFluctuation() {
    _priceFluctuation.stopPriceFluctuation();
    log('Price fluctuation stopped manually');
  }

  // Simulate market events
  void simulateMarketEvent(MarketEvent event) {
    final updatedShares = _priceFluctuation.simulateMarketEvent(_shares, event);
    _shares = updatedShares;
    onPricesUpdated?.call(_shares);
    log('Market event ${event.name} simulated');
  }

  // Manual price fluctuation
  void fluctuatePricesOnce() {
    final updatedShares = _priceFluctuation.fluctuatePricesOnce(_shares);
    _shares = updatedShares;
    onPricesUpdated?.call(_shares);
    log('Manual price fluctuation applied');
  }

  // Market control methods
  Future<void> openMarket() async {
    await _marketControl.openMarket();
    // Restart price fluctuation when market opens
    if (_marketControl.isMarketOpen) {
      _priceFluctuation.startPriceFluctuation(_shares);
    }
    log('Market opened');
  }

  Future<void> closeMarket() async {
    await _marketControl.closeMarket();
    // Stop price fluctuation when market closes
    _priceFluctuation.stopPriceFluctuation();
    log('Market closed');
  }

  Future<void> toggleMarket() async {
    await _marketControl.toggleMarket();
    // Update price fluctuation based on market status
    if (_marketControl.isMarketOpen) {
      _priceFluctuation.startPriceFluctuation(_shares);
    } else {
      _priceFluctuation.stopPriceFluctuation();
    }
    log('Market toggled to ${_marketControl.isMarketOpen ? "OPEN" : "CLOSED"}');
  }

  // Get market info
  Map<String, dynamic> get marketInfo => _marketControl.getMarketInfo();

  // Get fluctuation status
  bool get isPriceFluctuationActive => _priceFluctuation.isFluctuationActive;

  // Get fluctuation settings
  Map<String, dynamic> get fluctuationSettings =>
      _priceFluctuation.getFluctuationSettings();
}

class TransactionResult {
  final bool success;
  final String message;
  final Transaction? transaction;

  TransactionResult({
    required this.success,
    required this.message,
    this.transaction,
  });
}
