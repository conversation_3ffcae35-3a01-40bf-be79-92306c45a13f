import 'dart:developer';
import 'package:firebase_remote_config/firebase_remote_config.dart';

class FirebaseRemoteConfigService {
  static final FirebaseRemoteConfigService _instance =
      FirebaseRemoteConfigService._internal();
  factory FirebaseRemoteConfigService() => _instance;
  FirebaseRemoteConfigService._internal();

  late FirebaseRemoteConfig _remoteConfig;
  bool _initialized = false;

  // Default values for remote config
  static const Map<String, dynamic> _defaultValues = {
    // Share prices
    'price_TATA': 1250.50,
    'price_RELIANCE': 2890.75,
    'price_INFY': 1680.25,
    'price_TCS': 3750.80,
    'price_HDFC': 1580.40,
    'price_ICICI': 1120.60,
    'price_WIPRO': 580.30,
    'price_BHARTI': 920.15,
    'price_ITC': 485.90,
    'price_SBIN': 820.45,

    // Feature toggles
    'enable_sell_option': true,
    'enable_sorting': false,
    'enable_market_trend': false,
    'market_status': 'OPEN',
    'maintenance_mode': false,
    'auto_price_fluctuation': true,
    'price_fluctuation_interval': 2, // seconds - much faster fluctuation
    'max_price_change_percent': 10.0,

    // App configuration
    'min_transaction_amount': 100.0,
    'max_transaction_amount': 100000.0,
    'transaction_fee_percentage': 0.1,
  };

  // Initialize Remote Config
  Future<void> initialize() async {
    if (_initialized) return;

    try {
      _remoteConfig = FirebaseRemoteConfig.instance;

      // Set config settings
      await _remoteConfig.setConfigSettings(
        RemoteConfigSettings(
          fetchTimeout: const Duration(minutes: 1),
          minimumFetchInterval: const Duration(seconds: 10), // For development
        ),
      );

      // Set default values
      await _remoteConfig.setDefaults(_defaultValues);

      // Fetch and activate
      await fetchAndActivate();

      _initialized = true;
      log('Firebase Remote Config initialized successfully');
    } catch (e) {
      log('Error initializing Firebase Remote Config: $e');
      _initialized = false;
    }
  }

  // Fetch and activate remote config
  Future<bool> fetchAndActivate() async {
    try {
      final bool updated = await _remoteConfig.fetchAndActivate();
      log('Remote Config updated: $updated');
      return updated;
    } catch (e) {
      log('Error fetching remote config: $e');
      return false;
    }
  }

  // Get share price by symbol
  double getSharePrice(String symbol) {
    if (!_initialized) {
      log('Remote Config not initialized, using default value');
      return _defaultValues['price_$symbol'] as double? ?? 0.0;
    }

    try {
      return _remoteConfig.getDouble('price_$symbol');
    } catch (e) {
      log('Error getting share price for $symbol: $e');
      return _defaultValues['price_$symbol'] as double? ?? 0.0;
    }
  }

  // Get all share prices
  Map<String, double> getAllSharePrices() {
    final Map<String, double> prices = {};
    final List<String> symbols = [
      'TATA',
      'RELIANCE',
      'INFY',
      'TCS',
      'HDFC',
      'ICICI',
      'WIPRO',
      'BHARTI',
      'ITC',
      'SBIN',
    ];

    for (final symbol in symbols) {
      prices[symbol] = getSharePrice(symbol);
    }

    return prices;
  }

  // Feature toggles
  bool get isSellOptionEnabled {
    if (!_initialized) return _defaultValues['enable_sell_option'] as bool;
    return _remoteConfig.getBool('enable_sell_option');
  }

  bool get isSortingEnabled {
    if (!_initialized) return _defaultValues['enable_sorting'] as bool;
    return _remoteConfig.getBool('enable_sorting');
  }

  bool get isMarketTrendEnabled {
    if (!_initialized) return _defaultValues['enable_market_trend'] as bool;
    return _remoteConfig.getBool('enable_market_trend');
  }

  String get marketStatus {
    if (!_initialized) return _defaultValues['market_status'] as String;
    return _remoteConfig.getString('market_status');
  }

  bool get isMaintenanceMode {
    if (!_initialized) return _defaultValues['maintenance_mode'] as bool;
    return _remoteConfig.getBool('maintenance_mode');
  }

  // App configuration
  double get minTransactionAmount {
    if (!_initialized) {
      return _defaultValues['min_transaction_amount'] as double;
    }
    return _remoteConfig.getDouble('min_transaction_amount');
  }

  double get maxTransactionAmount {
    if (!_initialized) {
      return _defaultValues['max_transaction_amount'] as double;
    }
    return _remoteConfig.getDouble('max_transaction_amount');
  }

  double get transactionFeePercentage {
    if (!_initialized) {
      return _defaultValues['transaction_fee_percentage'] as double;
    }
    return _remoteConfig.getDouble('transaction_fee_percentage');
  }

  // Price fluctuation settings
  bool get isAutoPriceFluctuationEnabled {
    if (!_initialized) return _defaultValues['auto_price_fluctuation'] as bool;
    return _remoteConfig.getBool('auto_price_fluctuation');
  }

  int get priceFluctuationInterval {
    if (!_initialized) {
      return _defaultValues['price_fluctuation_interval'] as int;
    }
    return _remoteConfig.getInt('price_fluctuation_interval');
  }

  double get maxPriceChangePercent {
    if (!_initialized) {
      return _defaultValues['max_price_change_percent'] as double;
    }
    return _remoteConfig.getDouble('max_price_change_percent');
  }

  // Get all remote config values for debugging
  Map<String, dynamic> getAllValues() {
    if (!_initialized) return _defaultValues;

    final Map<String, dynamic> values = {};
    for (final key in _defaultValues.keys) {
      try {
        final value = _remoteConfig.getValue(key);
        values[key] = value.asString();
      } catch (e) {
        values[key] = _defaultValues[key];
      }
    }
    return values;
  }

  // Listen to remote config updates
  Stream<RemoteConfigUpdate> get onConfigUpdated {
    return _remoteConfig.onConfigUpdated;
  }

  // Activate fetched config
  Future<bool> activate() async {
    try {
      return await _remoteConfig.activate();
    } catch (e) {
      log('Error activating remote config: $e');
      return false;
    }
  }
}
