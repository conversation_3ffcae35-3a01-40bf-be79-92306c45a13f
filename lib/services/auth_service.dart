import 'dart:convert';
import 'dart:developer';
import 'dart:math' as math;
import 'package:crypto/crypto.dart';
import '../models/user.dart';
import 'local_storage_service.dart';

class AuthService {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  final LocalStorageService _localStorage = LocalStorageService();

  static const String _currentUserKey = 'current_user';
  static const String _usersKey = 'registered_users';
  static const String _isLoggedInKey = 'is_logged_in';

  User? _currentUser;
  bool _isLoggedIn = false;

  // Getters
  User? get currentUser => _currentUser;
  bool get isLoggedIn => _isLoggedIn;
  String get userDisplayName => _currentUser?.name ?? 'Guest';

  // Initialize auth service
  Future<void> initialize() async {
    try {
      await _localStorage.initialize();

      // Create demo user if it doesn't exist
      await _createDemoUserIfNeeded();

      // Check if user was previously logged in
      final isLoggedIn = await _localStorage.loadSetting<bool>(
        _isLoggedInKey,
        defaultValue: false,
      );

      if (isLoggedIn == true) {
        final userJson = await _localStorage.loadSetting<Map<String, dynamic>>(
          _currentUserKey,
        );
        if (userJson != null) {
          _currentUser = User.fromJson(userJson);
          _isLoggedIn = true;
          log('User restored from storage: ${_currentUser!.email}');
        }
      }

      log('Auth service initialized. Logged in: $_isLoggedIn');
    } catch (e) {
      log('Error initializing auth service: $e');
    }
  }

  // Register new user
  Future<AuthResult> register(RegisterCredentials credentials) async {
    try {
      if (!credentials.isValid) {
        return AuthResult(
          success: false,
          message: 'Please fill all fields correctly',
        );
      }

      // Check if user already exists
      final existingUsers = await _getRegisteredUsers();
      final userExists = existingUsers.any(
        (user) => user.email.toLowerCase() == credentials.email.toLowerCase(),
      );

      if (userExists) {
        return AuthResult(
          success: false,
          message: 'User with this email already exists',
        );
      }

      // Create new user
      final userId = _generateUserId();
      final now = DateTime.now();

      final newUser = User(
        id: userId,
        email: credentials.email.toLowerCase(),
        name: credentials.name,
        createdAt: now,
        lastLoginAt: now,
      );

      // Hash password and store user
      final hashedPassword = _hashPassword(credentials.password);
      final userData = {...newUser.toJson(), 'password': hashedPassword};

      // Save to registered users
      existingUsers.add(newUser);
      await _saveRegisteredUsers(existingUsers);
      await _localStorage.saveSetting('user_${newUser.id}_data', userData);

      log('User registered successfully: ${newUser.email}');

      return AuthResult(
        success: true,
        message: 'Registration successful',
        user: newUser,
      );
    } catch (e) {
      log('Error during registration: $e');
      return AuthResult(
        success: false,
        message: 'Registration failed: ${e.toString()}',
      );
    }
  }

  // Login user
  Future<AuthResult> login(AuthCredentials credentials) async {
    try {
      if (!credentials.isValid) {
        return AuthResult(
          success: false,
          message: 'Please enter valid email and password',
        );
      }

      // Get registered users
      final registeredUsers = await _getRegisteredUsers();
      final user = registeredUsers.firstWhere(
        (user) => user.email.toLowerCase() == credentials.email.toLowerCase(),
        orElse: () => throw Exception('User not found'),
      );

      // Get user data with password
      final userData = await _localStorage.loadSetting<Map<String, dynamic>>(
        'user_${user.id}_data',
      );
      if (userData == null) {
        throw Exception('User data not found');
      }

      // Verify password
      final storedPasswordHash = userData['password'] as String;
      final inputPasswordHash = _hashPassword(credentials.password);

      if (storedPasswordHash != inputPasswordHash) {
        return AuthResult(success: false, message: 'Invalid email or password');
      }

      // Update last login time
      final updatedUser = user.copyWith(lastLoginAt: DateTime.now());

      // Save current user
      _currentUser = updatedUser;
      _isLoggedIn = true;

      await _localStorage.saveSetting(_currentUserKey, updatedUser.toJson());
      await _localStorage.saveSetting(_isLoggedInKey, true);

      // Update user in registered users list
      final updatedUsers = registeredUsers
          .map((u) => u.id == updatedUser.id ? updatedUser : u)
          .toList();
      await _saveRegisteredUsers(updatedUsers);

      log('User logged in successfully: ${updatedUser.email}');

      return AuthResult(
        success: true,
        message: 'Login successful',
        user: updatedUser,
      );
    } catch (e) {
      log('Error during login: $e');
      return AuthResult(
        success: false,
        message: e.toString().contains('User not found')
            ? 'Invalid email or password'
            : 'Login failed: ${e.toString()}',
      );
    }
  }

  // Logout user
  Future<void> logout() async {
    try {
      _currentUser = null;
      _isLoggedIn = false;

      await _localStorage.saveSetting(_isLoggedInKey, false);
      await _localStorage.saveSetting(_currentUserKey, null);

      log('User logged out successfully');
    } catch (e) {
      log('Error during logout: $e');
    }
  }

  // Helper methods
  String _generateUserId() {
    final random = math.Random();
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final randomNum = random.nextInt(9999);
    return 'user_${timestamp}_$randomNum';
  }

  String _hashPassword(String password) {
    final bytes = utf8.encode('${password}stock_market_salt');
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  // Create demo user for easy testing
  Future<void> _createDemoUserIfNeeded() async {
    try {
      final existingUsers = await _getRegisteredUsers();
      final demoUserExists = existingUsers.any(
        (user) => user.email == '<EMAIL>',
      );

      if (!demoUserExists) {
        final demoUser = User(
          id: 'demo_user_123',
          email: '<EMAIL>',
          name: 'Demo User',
          createdAt: DateTime.now(),
          lastLoginAt: DateTime.now(),
        );

        final hashedPassword = _hashPassword('demo123');
        final userData = {...demoUser.toJson(), 'password': hashedPassword};

        existingUsers.add(demoUser);
        await _saveRegisteredUsers(existingUsers);
        await _localStorage.saveSetting('user_${demoUser.id}_data', userData);

        log('Demo user created: <EMAIL> / demo123');
      }
    } catch (e) {
      log('Error creating demo user: $e');
    }
  }

  Future<List<User>> _getRegisteredUsers() async {
    try {
      final usersJson = await _localStorage.loadSetting<List<dynamic>>(
        _usersKey,
        defaultValue: <dynamic>[],
      );
      return usersJson!
          .map((json) => User.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      log('Error getting registered users: $e');
      return [];
    }
  }

  Future<void> _saveRegisteredUsers(List<User> users) async {
    try {
      final usersJson = users.map((user) => user.toJson()).toList();
      await _localStorage.saveSetting(_usersKey, usersJson);
    } catch (e) {
      log('Error saving registered users: $e');
    }
  }

  // Get user stats
  Future<Map<String, dynamic>> getUserStats() async {
    final users = await _getRegisteredUsers();
    return {
      'totalUsers': users.length,
      'currentUser': _currentUser?.toJson(),
      'isLoggedIn': _isLoggedIn,
    };
  }
}

class AuthResult {
  final bool success;
  final String message;
  final User? user;

  AuthResult({required this.success, required this.message, this.user});
}
