import 'dart:convert';
import 'dart:developer';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_portfolio.dart';
import '../models/share.dart';

class LocalStorageService {
  static final LocalStorageService _instance = LocalStorageService._internal();
  factory LocalStorageService() => _instance;
  LocalStorageService._internal();

  static const String _portfolioKey = 'user_portfolio';
  static const String _sharesKey = 'shares_data';
  static const String _lastUpdateKey = 'last_price_update';

  SharedPreferences? _prefs;

  // Initialize shared preferences
  Future<void> initialize() async {
    try {
      _prefs = await SharedPreferences.getInstance();
      log('Local storage initialized successfully');
    } catch (e) {
      log('Error initializing local storage: $e');
    }
  }

  // Save user portfolio
  Future<bool> savePortfolio(UserPortfolio portfolio) async {
    try {
      if (_prefs == null) await initialize();
      
      final String portfolioJson = jsonEncode(portfolio.toJson());
      final bool result = await _prefs!.setString(_portfolioKey, portfolioJson);
      
      if (result) {
        log('Portfolio saved successfully');
      } else {
        log('Failed to save portfolio');
      }
      
      return result;
    } catch (e) {
      log('Error saving portfolio: $e');
      return false;
    }
  }

  // Load user portfolio
  Future<UserPortfolio> loadPortfolio() async {
    try {
      if (_prefs == null) await initialize();
      
      final String? portfolioJson = _prefs!.getString(_portfolioKey);
      
      if (portfolioJson != null) {
        final Map<String, dynamic> portfolioMap = jsonDecode(portfolioJson);
        final portfolio = UserPortfolio.fromJson(portfolioMap);
        log('Portfolio loaded successfully');
        return portfolio;
      } else {
        log('No saved portfolio found, creating new one');
        return UserPortfolio();
      }
    } catch (e) {
      log('Error loading portfolio: $e');
      return UserPortfolio();
    }
  }

  // Save shares data (for caching)
  Future<bool> saveShares(Map<String, Share> shares) async {
    try {
      if (_prefs == null) await initialize();
      
      final Map<String, dynamic> sharesMap = {};
      shares.forEach((key, value) {
        sharesMap[key] = value.toJson();
      });
      
      final String sharesJson = jsonEncode(sharesMap);
      final bool result = await _prefs!.setString(_sharesKey, sharesJson);
      
      if (result) {
        // Also save the last update timestamp
        await _prefs!.setInt(_lastUpdateKey, DateTime.now().millisecondsSinceEpoch);
        log('Shares data saved successfully');
      } else {
        log('Failed to save shares data');
      }
      
      return result;
    } catch (e) {
      log('Error saving shares data: $e');
      return false;
    }
  }

  // Load shares data
  Future<Map<String, Share>?> loadShares() async {
    try {
      if (_prefs == null) await initialize();
      
      final String? sharesJson = _prefs!.getString(_sharesKey);
      
      if (sharesJson != null) {
        final Map<String, dynamic> sharesMap = jsonDecode(sharesJson);
        final Map<String, Share> shares = {};
        
        sharesMap.forEach((key, value) {
          shares[key] = Share.fromJson(value as Map<String, dynamic>);
        });
        
        log('Shares data loaded successfully');
        return shares;
      } else {
        log('No cached shares data found');
        return null;
      }
    } catch (e) {
      log('Error loading shares data: $e');
      return null;
    }
  }

  // Get last price update timestamp
  Future<DateTime?> getLastPriceUpdate() async {
    try {
      if (_prefs == null) await initialize();
      
      final int? timestamp = _prefs!.getInt(_lastUpdateKey);
      
      if (timestamp != null) {
        return DateTime.fromMillisecondsSinceEpoch(timestamp);
      }
      
      return null;
    } catch (e) {
      log('Error getting last price update: $e');
      return null;
    }
  }

  // Clear all data
  Future<bool> clearAllData() async {
    try {
      if (_prefs == null) await initialize();
      
      await _prefs!.remove(_portfolioKey);
      await _prefs!.remove(_sharesKey);
      await _prefs!.remove(_lastUpdateKey);
      
      log('All data cleared successfully');
      return true;
    } catch (e) {
      log('Error clearing data: $e');
      return false;
    }
  }

  // Save app settings
  Future<bool> saveSetting(String key, dynamic value) async {
    try {
      if (_prefs == null) await initialize();
      
      if (value is String) {
        return await _prefs!.setString(key, value);
      } else if (value is int) {
        return await _prefs!.setInt(key, value);
      } else if (value is double) {
        return await _prefs!.setDouble(key, value);
      } else if (value is bool) {
        return await _prefs!.setBool(key, value);
      } else {
        // For complex objects, convert to JSON
        return await _prefs!.setString(key, jsonEncode(value));
      }
    } catch (e) {
      log('Error saving setting $key: $e');
      return false;
    }
  }

  // Load app setting
  Future<T?> loadSetting<T>(String key, {T? defaultValue}) async {
    try {
      if (_prefs == null) await initialize();
      
      if (T == String) {
        return _prefs!.getString(key) as T? ?? defaultValue;
      } else if (T == int) {
        return _prefs!.getInt(key) as T? ?? defaultValue;
      } else if (T == double) {
        return _prefs!.getDouble(key) as T? ?? defaultValue;
      } else if (T == bool) {
        return _prefs!.getBool(key) as T? ?? defaultValue;
      } else {
        // For complex objects, decode from JSON
        final String? jsonString = _prefs!.getString(key);
        if (jsonString != null) {
          return jsonDecode(jsonString) as T;
        }
        return defaultValue;
      }
    } catch (e) {
      log('Error loading setting $key: $e');
      return defaultValue;
    }
  }

  // Check if data exists
  Future<bool> hasPortfolio() async {
    try {
      if (_prefs == null) await initialize();
      return _prefs!.containsKey(_portfolioKey);
    } catch (e) {
      log('Error checking portfolio existence: $e');
      return false;
    }
  }

  Future<bool> hasShares() async {
    try {
      if (_prefs == null) await initialize();
      return _prefs!.containsKey(_sharesKey);
    } catch (e) {
      log('Error checking shares existence: $e');
      return false;
    }
  }
}
