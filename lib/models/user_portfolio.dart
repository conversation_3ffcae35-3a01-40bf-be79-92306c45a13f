import 'share.dart';

class ShareHolding {
  final String symbol;
  final int quantity;
  final double averagePrice;
  final DateTime lastTransactionDate;

  ShareHolding({
    required this.symbol,
    required this.quantity,
    required this.averagePrice,
    required this.lastTransactionDate,
  });

  // Calculate current value based on current share price
  double getCurrentValue(double currentPrice) {
    return quantity * currentPrice;
  }

  // Calculate profit/loss
  double getProfitLoss(double currentPrice) {
    return (currentPrice - averagePrice) * quantity;
  }

  // Calculate profit/loss percentage
  double getProfitLossPercentage(double currentPrice) {
    if (averagePrice == 0) return 0.0;
    return ((currentPrice - averagePrice) / averagePrice) * 100;
  }

  // Create a copy with updated values
  ShareHolding copyWith({
    String? symbol,
    int? quantity,
    double? averagePrice,
    DateTime? lastTransactionDate,
  }) {
    return ShareHolding(
      symbol: symbol ?? this.symbol,
      quantity: quantity ?? this.quantity,
      averagePrice: averagePrice ?? this.averagePrice,
      lastTransactionDate: lastTransactionDate ?? this.lastTransactionDate,
    );
  }

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'symbol': symbol,
      'quantity': quantity,
      'averagePrice': averagePrice,
      'lastTransactionDate': lastTransactionDate.toIso8601String(),
    };
  }

  // Create from JSON
  factory ShareHolding.fromJson(Map<String, dynamic> json) {
    return ShareHolding(
      symbol: json['symbol'] as String,
      quantity: json['quantity'] as int,
      averagePrice: (json['averagePrice'] as num).toDouble(),
      lastTransactionDate: DateTime.parse(json['lastTransactionDate'] as String),
    );
  }

  @override
  String toString() {
    return 'ShareHolding(symbol: $symbol, quantity: $quantity, averagePrice: $averagePrice, lastTransactionDate: $lastTransactionDate)';
  }
}

class Transaction {
  final String id;
  final String symbol;
  final TransactionType type;
  final int quantity;
  final double price;
  final DateTime timestamp;

  Transaction({
    required this.id,
    required this.symbol,
    required this.type,
    required this.quantity,
    required this.price,
    required this.timestamp,
  });

  double get totalAmount => quantity * price;

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'symbol': symbol,
      'type': type.name,
      'quantity': quantity,
      'price': price,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  // Create from JSON
  factory Transaction.fromJson(Map<String, dynamic> json) {
    return Transaction(
      id: json['id'] as String,
      symbol: json['symbol'] as String,
      type: TransactionType.values.firstWhere(
        (e) => e.name == json['type'],
      ),
      quantity: json['quantity'] as int,
      price: (json['price'] as num).toDouble(),
      timestamp: DateTime.parse(json['timestamp'] as String),
    );
  }
}

enum TransactionType { buy, sell }

class UserPortfolio {
  final Map<String, ShareHolding> holdings;
  final List<Transaction> transactions;
  final double cashBalance;

  UserPortfolio({
    Map<String, ShareHolding>? holdings,
    List<Transaction>? transactions,
    this.cashBalance = 100000.0, // Starting with ₹1,00,000
  })  : holdings = holdings ?? {},
        transactions = transactions ?? [];

  // Get total portfolio value
  double getTotalValue(Map<String, Share> shares) {
    double totalValue = cashBalance;
    for (final holding in holdings.values) {
      final share = shares[holding.symbol];
      if (share != null) {
        totalValue += holding.getCurrentValue(share.currentPrice);
      }
    }
    return totalValue;
  }

  // Get total invested amount
  double getTotalInvested() {
    double totalInvested = 0.0;
    for (final holding in holdings.values) {
      totalInvested += holding.quantity * holding.averagePrice;
    }
    return totalInvested;
  }

  // Get total profit/loss
  double getTotalProfitLoss(Map<String, Share> shares) {
    double totalPL = 0.0;
    for (final holding in holdings.values) {
      final share = shares[holding.symbol];
      if (share != null) {
        totalPL += holding.getProfitLoss(share.currentPrice);
      }
    }
    return totalPL;
  }

  // Check if user owns a share
  bool ownsShare(String symbol) {
    return holdings.containsKey(symbol) && holdings[symbol]!.quantity > 0;
  }

  // Get quantity of owned shares
  int getOwnedQuantity(String symbol) {
    return holdings[symbol]?.quantity ?? 0;
  }

  // Create a copy with updated values
  UserPortfolio copyWith({
    Map<String, ShareHolding>? holdings,
    List<Transaction>? transactions,
    double? cashBalance,
  }) {
    return UserPortfolio(
      holdings: holdings ?? Map.from(this.holdings),
      transactions: transactions ?? List.from(this.transactions),
      cashBalance: cashBalance ?? this.cashBalance,
    );
  }

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'holdings': holdings.map((key, value) => MapEntry(key, value.toJson())),
      'transactions': transactions.map((t) => t.toJson()).toList(),
      'cashBalance': cashBalance,
    };
  }

  // Create from JSON
  factory UserPortfolio.fromJson(Map<String, dynamic> json) {
    final holdingsMap = <String, ShareHolding>{};
    final holdingsJson = json['holdings'] as Map<String, dynamic>? ?? {};
    
    for (final entry in holdingsJson.entries) {
      holdingsMap[entry.key] = ShareHolding.fromJson(entry.value as Map<String, dynamic>);
    }

    final transactionsList = <Transaction>[];
    final transactionsJson = json['transactions'] as List<dynamic>? ?? [];
    
    for (final transactionJson in transactionsJson) {
      transactionsList.add(Transaction.fromJson(transactionJson as Map<String, dynamic>));
    }

    return UserPortfolio(
      holdings: holdingsMap,
      transactions: transactionsList,
      cashBalance: (json['cashBalance'] as num?)?.toDouble() ?? 100000.0,
    );
  }
}
