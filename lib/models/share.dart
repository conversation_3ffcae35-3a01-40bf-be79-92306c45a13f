class Share {
  final String symbol;
  final String name;
  final double currentPrice;
  final double previousPrice;
  final String sector;

  Share({
    required this.symbol,
    required this.name,
    required this.currentPrice,
    required this.previousPrice,
    required this.sector,
  });

  // Calculate price change percentage
  double get priceChangePercentage {
    if (previousPrice == 0) return 0.0;
    return ((currentPrice - previousPrice) / previousPrice) * 100;
  }

  // Check if price went up
  bool get isPriceUp => currentPrice > previousPrice;

  // Check if price went down
  bool get isPriceDown => currentPrice < previousPrice;

  // Get price change amount
  double get priceChange => currentPrice - previousPrice;

  // Create a copy with updated price
  Share copyWith({
    String? symbol,
    String? name,
    double? currentPrice,
    double? previousPrice,
    String? sector,
  }) {
    return Share(
      symbol: symbol ?? this.symbol,
      name: name ?? this.name,
      currentPrice: currentPrice ?? this.currentPrice,
      previousPrice: previousPrice ?? this.previousPrice,
      sector: sector ?? this.sector,
    );
  }

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'symbol': symbol,
      'name': name,
      'currentPrice': currentPrice,
      'previousPrice': previousPrice,
      'sector': sector,
    };
  }

  // Create from JSON
  factory Share.fromJson(Map<String, dynamic> json) {
    return Share(
      symbol: json['symbol'] as String,
      name: json['name'] as String,
      currentPrice: (json['currentPrice'] as num).toDouble(),
      previousPrice: (json['previousPrice'] as num).toDouble(),
      sector: json['sector'] as String,
    );
  }

  @override
  String toString() {
    return 'Share(symbol: $symbol, name: $name, currentPrice: $currentPrice, previousPrice: $previousPrice, sector: $sector)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Share && other.symbol == symbol;
  }

  @override
  int get hashCode => symbol.hashCode;
}
