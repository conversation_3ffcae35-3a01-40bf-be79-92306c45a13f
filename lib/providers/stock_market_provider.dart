import 'dart:async';
import 'dart:developer';
import 'package:flutter/foundation.dart';
import '../models/share.dart';
import '../models/user_portfolio.dart';
import '../services/stock_market_service.dart';
import '../services/price_fluctuation_service.dart';

class StockMarketProvider extends ChangeNotifier {
  final StockMarketService _stockMarketService = StockMarketService();

  bool _isLoading = false;
  String? _error;
  Timer? _priceUpdateTimer;

  bool get isLoading => _isLoading;
  String? get error => _error;

  Map<String, Share> get shares => _stockMarketService.shares;
  UserPortfolio get portfolio => _stockMarketService.portfolio;

  // Feature flags
  bool get isSellOptionEnabled => _stockMarketService.isSellOptionEnabled;
  bool get isSortingEnabled => _stockMarketService.isSortingEnabled;
  bool get isMarketTrendEnabled => _stockMarketService.isMarketTrendEnabled;
  String get marketStatus => _stockMarketService.marketStatus;
  bool get isMaintenanceMode => _stockMarketService.isMaintenanceMode;

  // Initialize the provider
  Future<void> initialize() async {
    if (_isLoading) return;

    _setLoading(true);
    _setError(null);

    try {
      // Setup callback for automatic price updates
      _stockMarketService.onPricesUpdated = (updatedShares) {
        notifyListeners();
      };

      await _stockMarketService.initialize();
      _startPriceUpdateTimer();
      log('Stock market provider initialized successfully');
    } catch (e) {
      _setError('Failed to initialize: ${e.toString()}');
      log('Error initializing stock market provider: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Update share prices
  Future<void> updateSharePrices() async {
    try {
      await _stockMarketService.updateSharePrices();
      notifyListeners();
      log('Share prices updated');
    } catch (e) {
      _setError('Failed to update prices: ${e.toString()}');
      log('Error updating share prices: $e');
    }
  }

  // Buy shares
  Future<TransactionResult> buyShares(String symbol, int quantity) async {
    try {
      final result = await _stockMarketService.buyShares(symbol, quantity);
      if (result.success) {
        notifyListeners();
      }
      return result;
    } catch (e) {
      log('Error buying shares: $e');
      return TransactionResult(
        success: false,
        message: 'Transaction failed: ${e.toString()}',
      );
    }
  }

  // Sell shares
  Future<TransactionResult> sellShares(String symbol, int quantity) async {
    try {
      final result = await _stockMarketService.sellShares(symbol, quantity);
      if (result.success) {
        notifyListeners();
      }
      return result;
    } catch (e) {
      log('Error selling shares: $e');
      return TransactionResult(
        success: false,
        message: 'Transaction failed: ${e.toString()}',
      );
    }
  }

  // Reset portfolio
  Future<void> resetPortfolio() async {
    try {
      await _stockMarketService.resetPortfolio();
      notifyListeners();
      log('Portfolio reset successfully');
    } catch (e) {
      _setError('Failed to reset portfolio: ${e.toString()}');
      log('Error resetting portfolio: $e');
    }
  }

  // Start automatic price updates
  void _startPriceUpdateTimer() {
    _priceUpdateTimer?.cancel();
    _priceUpdateTimer = Timer.periodic(
      const Duration(minutes: 1), // Update every minute
      (timer) {
        if (!isMaintenanceMode && marketStatus == 'OPEN') {
          updateSharePrices();
        }
      },
    );
  }

  // Stop automatic price updates
  void _stopPriceUpdateTimer() {
    _priceUpdateTimer?.cancel();
    _priceUpdateTimer = null;
  }

  // Set loading state
  void _setLoading(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
      notifyListeners();
    }
  }

  // Set error state
  void _setError(String? error) {
    if (_error != error) {
      _error = error;
      notifyListeners();
    }
  }

  // Clear error
  void clearError() {
    _setError(null);
  }

  // Refresh all data
  Future<void> refresh() async {
    await updateSharePrices();
  }

  // Price fluctuation controls
  void startPriceFluctuation() {
    try {
      _stockMarketService.startPriceFluctuation();
      log('Price fluctuation started');
    } catch (e) {
      log('Error starting price fluctuation: $e');
    }
  }

  void stopPriceFluctuation() {
    try {
      _stockMarketService.stopPriceFluctuation();
      log('Price fluctuation stopped');
    } catch (e) {
      log('Error stopping price fluctuation: $e');
    }
  }

  // Market event simulation
  void simulateMarketEvent(MarketEvent event) {
    try {
      _stockMarketService.simulateMarketEvent(event);
      log('Market event ${event.name} simulated');
    } catch (e) {
      log('Error simulating market event: $e');
    }
  }

  // Manual price fluctuation
  void fluctuatePricesOnce() {
    try {
      _stockMarketService.fluctuatePricesOnce();
      log('Manual price fluctuation applied');
    } catch (e) {
      log('Error applying manual fluctuation: $e');
    }
  }

  // Market control methods
  Future<void> openMarket() async {
    try {
      await _stockMarketService.openMarket();
      notifyListeners();
      log('Market opened');
    } catch (e) {
      log('Error opening market: $e');
    }
  }

  Future<void> closeMarket() async {
    try {
      await _stockMarketService.closeMarket();
      notifyListeners();
      log('Market closed');
    } catch (e) {
      log('Error closing market: $e');
    }
  }

  Future<void> toggleMarket() async {
    try {
      await _stockMarketService.toggleMarket();
      notifyListeners();
      log('Market toggled');
    } catch (e) {
      log('Error toggling market: $e');
    }
  }

  // Get market status
  bool get isMarketOpen => _stockMarketService.isMarketOpen;
  Map<String, dynamic> get marketInfo => _stockMarketService.marketInfo;

  // Get fluctuation status
  bool get isPriceFluctuationActive =>
      _stockMarketService.isPriceFluctuationActive;

  // Get fluctuation settings
  Map<String, dynamic> get fluctuationSettings =>
      _stockMarketService.fluctuationSettings;

  @override
  void dispose() {
    _stopPriceUpdateTimer();
    super.dispose();
  }
}
