import 'dart:developer';
import 'package:flutter/foundation.dart';
import '../models/user.dart';
import '../services/auth_service.dart';

class AuthProvider extends ChangeNotifier {
  final AuthService _authService = AuthService();
  
  bool _isLoading = false;
  String? _error;

  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isLoggedIn => _authService.isLoggedIn;
  User? get currentUser => _authService.currentUser;
  String get userDisplayName => _authService.userDisplayName;

  // Initialize the provider
  Future<void> initialize() async {
    _setLoading(true);
    _setError(null);
    
    try {
      await _authService.initialize();
      log('Auth provider initialized successfully');
    } catch (e) {
      _setError('Failed to initialize: ${e.toString()}');
      log('Error initializing auth provider: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Register new user
  Future<AuthResult> register(RegisterCredentials credentials) async {
    _setLoading(true);
    _setError(null);
    
    try {
      final result = await _authService.register(credentials);
      
      if (result.success) {
        notifyListeners(); // User registered but not logged in yet
      } else {
        _setError(result.message);
      }
      
      return result;
    } catch (e) {
      final errorMessage = 'Registration failed: ${e.toString()}';
      _setError(errorMessage);
      log('Error during registration: $e');
      
      return AuthResult(
        success: false,
        message: errorMessage,
      );
    } finally {
      _setLoading(false);
    }
  }

  // Login user
  Future<AuthResult> login(AuthCredentials credentials) async {
    _setLoading(true);
    _setError(null);
    
    try {
      final result = await _authService.login(credentials);
      
      if (result.success) {
        notifyListeners(); // User state changed
      } else {
        _setError(result.message);
      }
      
      return result;
    } catch (e) {
      final errorMessage = 'Login failed: ${e.toString()}';
      _setError(errorMessage);
      log('Error during login: $e');
      
      return AuthResult(
        success: false,
        message: errorMessage,
      );
    } finally {
      _setLoading(false);
    }
  }

  // Logout user
  Future<void> logout() async {
    _setLoading(true);
    _setError(null);
    
    try {
      await _authService.logout();
      notifyListeners(); // User state changed
      log('User logged out successfully');
    } catch (e) {
      _setError('Logout failed: ${e.toString()}');
      log('Error during logout: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Set loading state
  void _setLoading(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
      notifyListeners();
    }
  }

  // Set error state
  void _setError(String? error) {
    if (_error != error) {
      _error = error;
      notifyListeners();
    }
  }

  // Clear error
  void clearError() {
    _setError(null);
  }

  // Get user statistics
  Future<Map<String, dynamic>> getUserStats() async {
    return await _authService.getUserStats();
  }
}
