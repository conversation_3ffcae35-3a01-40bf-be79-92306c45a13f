import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/user_portfolio.dart';
import '../providers/stock_market_provider.dart';
import '../widgets/portfolio_holding_card.dart';
import '../widgets/transaction_history_card.dart';

class PortfolioScreen extends StatefulWidget {
  const PortfolioScreen({super.key});

  @override
  State<PortfolioScreen> createState() => _PortfolioScreenState();
}

class _PortfolioScreenState extends State<PortfolioScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('My Portfolio'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.pie_chart), text: 'Holdings'),
            Tab(icon: Icon(Icons.history), text: 'Transactions'),
          ],
        ),
        actions: [
          PopupMenuButton<String>(
            onSelected: (value) {
              if (value == 'reset') {
                _showResetConfirmation();
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'reset',
                child: Row(
                  children: [
                    Icon(Icons.refresh, color: Colors.red),
                    SizedBox(width: 8),
                    Text('Reset Portfolio'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Consumer<StockMarketProvider>(
        builder: (context, provider, child) {
          final portfolio = provider.portfolio;
          final shares = provider.shares;

          return Column(
            children: [
              // Portfolio summary
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                child: Card(
                  elevation: 4,
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text(
                              'Total Portfolio Value',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            Text(
                              '₹${portfolio.getTotalValue(shares).toStringAsFixed(2)}',
                              style: const TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color: Colors.blue,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  'Cash Balance',
                                  style: TextStyle(fontSize: 14, color: Colors.grey),
                                ),
                                Text(
                                  '₹${portfolio.cashBalance.toStringAsFixed(2)}',
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.green,
                                  ),
                                ),
                              ],
                            ),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: [
                                const Text(
                                  'Invested Amount',
                                  style: TextStyle(fontSize: 14, color: Colors.grey),
                                ),
                                Text(
                                  '₹${portfolio.getTotalInvested().toStringAsFixed(2)}',
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.orange,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: portfolio.getTotalProfitLoss(shares) >= 0
                                ? Colors.green.shade50
                                : Colors.red.shade50,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: portfolio.getTotalProfitLoss(shares) >= 0
                                  ? Colors.green.shade200
                                  : Colors.red.shade200,
                            ),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                portfolio.getTotalProfitLoss(shares) >= 0
                                    ? 'Total Profit'
                                    : 'Total Loss',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: portfolio.getTotalProfitLoss(shares) >= 0
                                      ? Colors.green.shade800
                                      : Colors.red.shade800,
                                ),
                              ),
                              Row(
                                children: [
                                  Icon(
                                    portfolio.getTotalProfitLoss(shares) >= 0
                                        ? Icons.trending_up
                                        : Icons.trending_down,
                                    color: portfolio.getTotalProfitLoss(shares) >= 0
                                        ? Colors.green
                                        : Colors.red,
                                    size: 20,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    '₹${portfolio.getTotalProfitLoss(shares).abs().toStringAsFixed(2)}',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                      color: portfolio.getTotalProfitLoss(shares) >= 0
                                          ? Colors.green
                                          : Colors.red,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              // Tab content
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    // Holdings tab
                    _buildHoldingsTab(portfolio, shares),
                    // Transactions tab
                    _buildTransactionsTab(portfolio.transactions),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildHoldingsTab(UserPortfolio portfolio, Map<String, dynamic> shares) {
    if (portfolio.holdings.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.account_balance_wallet_outlined, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'No holdings yet',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text('Start buying shares to build your portfolio'),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: portfolio.holdings.length,
      itemBuilder: (context, index) {
        final holding = portfolio.holdings.values.elementAt(index);
        final share = shares[holding.symbol];
        
        return PortfolioHoldingCard(
          holding: holding,
          share: share,
        );
      },
    );
  }

  Widget _buildTransactionsTab(List<Transaction> transactions) {
    if (transactions.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.history, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'No transactions yet',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text('Your transaction history will appear here'),
          ],
        ),
      );
    }

    // Sort transactions by timestamp (newest first)
    final sortedTransactions = List<Transaction>.from(transactions)
      ..sort((a, b) => b.timestamp.compareTo(a.timestamp));

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: sortedTransactions.length,
      itemBuilder: (context, index) {
        final transaction = sortedTransactions[index];
        return TransactionHistoryCard(transaction: transaction);
      },
    );
  }

  void _showResetConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset Portfolio'),
        content: const Text(
          'Are you sure you want to reset your portfolio? This will clear all holdings and transactions, and restore your cash balance to ₹1,00,000.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              context.read<StockMarketProvider>().resetPortfolio();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Portfolio reset successfully'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: const Text('Reset', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }
}
