import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/share.dart';
import '../models/user_portfolio.dart';
import '../providers/stock_market_provider.dart';
import '../providers/auth_provider.dart';
import '../services/price_fluctuation_service.dart';
import '../widgets/share_card.dart';
import '../widgets/buy_sell_dialog.dart';
import 'portfolio_screen.dart';

class ShareListScreen extends StatefulWidget {
  const ShareListScreen({super.key});

  @override
  State<ShareListScreen> createState() => _ShareListScreenState();
}

class _ShareListScreenState extends State<ShareListScreen> {
  String _sortBy = 'symbol'; // symbol, price, change
  bool _sortAscending = true;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<StockMarketProvider>().initialize();
    });
  }

  List<Share> _getSortedShares(
    List<Share> shares,
    StockMarketProvider provider,
  ) {
    if (!provider.isSortingEnabled) return shares;

    final sortedShares = List<Share>.from(shares);

    switch (_sortBy) {
      case 'symbol':
        sortedShares.sort(
          (a, b) => _sortAscending
              ? a.symbol.compareTo(b.symbol)
              : b.symbol.compareTo(a.symbol),
        );
        break;
      case 'price':
        sortedShares.sort(
          (a, b) => _sortAscending
              ? a.currentPrice.compareTo(b.currentPrice)
              : b.currentPrice.compareTo(a.currentPrice),
        );
        break;
      case 'change':
        sortedShares.sort(
          (a, b) => _sortAscending
              ? a.priceChangePercentage.compareTo(b.priceChangePercentage)
              : b.priceChangePercentage.compareTo(a.priceChangePercentage),
        );
        break;
    }

    return sortedShares;
  }

  void _showBuyDialog(Share share) {
    showDialog(
      context: context,
      builder: (context) =>
          BuySellDialog(share: share, transactionType: TransactionType.buy),
    );
  }

  void _showSellDialog(Share share) {
    showDialog(
      context: context,
      builder: (context) =>
          BuySellDialog(share: share, transactionType: TransactionType.sell),
    );
  }

  Widget _buildActionButtons() {
    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          bottom: BorderSide(color: Theme.of(context).dividerColor, width: 0.5),
        ),
      ),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: [
            // User profile button
            Consumer<AuthProvider>(
              builder: (context, authProvider, child) {
                return PopupMenuButton<String>(
                  icon: CircleAvatar(
                    backgroundColor: Theme.of(context).primaryColor,
                    radius: 18,
                    child: Text(
                      authProvider.userDisplayName.isNotEmpty
                          ? authProvider.userDisplayName[0].toUpperCase()
                          : 'U',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ),
                  onSelected: (value) async {
                    if (value == 'logout') {
                      await authProvider.logout();
                    }
                  },
                  itemBuilder: (context) => [
                    PopupMenuItem(
                      enabled: false,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            authProvider.userDisplayName,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                          if (authProvider.currentUser != null)
                            Text(
                              authProvider.currentUser!.email,
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 14,
                              ),
                            ),
                        ],
                      ),
                    ),
                    const PopupMenuDivider(),
                    const PopupMenuItem(
                      value: 'logout',
                      child: Row(
                        children: [
                          Icon(Icons.logout, color: Colors.red),
                          SizedBox(width: 8),
                          Text('Logout'),
                        ],
                      ),
                    ),
                  ],
                );
              },
            ),
            const SizedBox(width: 8),

            // Sort button
            Consumer<StockMarketProvider>(
              builder: (context, provider, child) {
                if (provider.isSortingEnabled) {
                  return Container(
                    decoration: BoxDecoration(
                      color: Theme.of(context).primaryColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: PopupMenuButton<String>(
                      icon: const Icon(Icons.sort),
                      tooltip: 'Sort Options',
                      onSelected: (value) {
                        setState(() {
                          if (_sortBy == value) {
                            _sortAscending = !_sortAscending;
                          } else {
                            _sortBy = value;
                            _sortAscending = true;
                          }
                        });
                      },
                      itemBuilder: (context) => [
                        PopupMenuItem(
                          value: 'symbol',
                          child: Row(
                            children: [
                              const Icon(Icons.abc),
                              const SizedBox(width: 8),
                              const Text('Symbol'),
                              if (_sortBy == 'symbol') ...[
                                const Spacer(),
                                Icon(
                                  _sortAscending
                                      ? Icons.arrow_upward
                                      : Icons.arrow_downward,
                                ),
                              ],
                            ],
                          ),
                        ),
                        PopupMenuItem(
                          value: 'price',
                          child: Row(
                            children: [
                              const Icon(Icons.currency_rupee),
                              const SizedBox(width: 8),
                              const Text('Price'),
                              if (_sortBy == 'price') ...[
                                const Spacer(),
                                Icon(
                                  _sortAscending
                                      ? Icons.arrow_upward
                                      : Icons.arrow_downward,
                                ),
                              ],
                            ],
                          ),
                        ),
                        PopupMenuItem(
                          value: 'change',
                          child: Row(
                            children: [
                              const Icon(Icons.trending_up),
                              const SizedBox(width: 8),
                              const Text('Change %'),
                              if (_sortBy == 'change') ...[
                                const Spacer(),
                                Icon(
                                  _sortAscending
                                      ? Icons.arrow_upward
                                      : Icons.arrow_downward,
                                ),
                              ],
                            ],
                          ),
                        ),
                      ],
                    ),
                  );
                }
                return const SizedBox.shrink();
              },
            ),
            const SizedBox(width: 8),

            // Refresh button
            Container(
              decoration: BoxDecoration(
                color: Colors.blue.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: IconButton(
                icon: const Icon(Icons.refresh, color: Colors.blue),
                tooltip: 'Refresh Prices',
                onPressed: () {
                  context.read<StockMarketProvider>().updateSharePrices();
                },
              ),
            ),
            const SizedBox(width: 8),

            // Market Open/Close Toggle
            Consumer<StockMarketProvider>(
              builder: (context, provider, child) {
                return Container(
                  decoration: BoxDecoration(
                    color: provider.isMarketOpen
                        ? Colors.green.withOpacity(0.1)
                        : Colors.red.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: IconButton(
                    icon: Icon(
                      provider.isMarketOpen
                          ? Icons.store
                          : Icons.store_mall_directory_outlined,
                      color: provider.isMarketOpen ? Colors.green : Colors.red,
                    ),
                    tooltip: provider.isMarketOpen
                        ? 'Close Market'
                        : 'Open Market',
                    onPressed: () async {
                      await provider.toggleMarket();
                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                              provider.isMarketOpen
                                  ? '🟢 Market is now OPEN! Trading enabled.'
                                  : '🔴 Market is now CLOSED! Trading disabled.',
                            ),
                            backgroundColor: provider.isMarketOpen
                                ? Colors.green
                                : Colors.red,
                          ),
                        );
                      }
                    },
                  ),
                );
              },
            ),
            const SizedBox(width: 8),

            // Market control menu
            Consumer<StockMarketProvider>(
              builder: (context, provider, child) {
                return Container(
                  decoration: BoxDecoration(
                    color: provider.isPriceFluctuationActive
                        ? Colors.green.withOpacity(0.1)
                        : Colors.grey.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: PopupMenuButton<String>(
                    icon: Icon(
                      provider.isPriceFluctuationActive
                          ? Icons.trending_up
                          : Icons.trending_flat,
                      color: provider.isPriceFluctuationActive
                          ? Colors.green
                          : Colors.grey,
                    ),
                    tooltip: 'Market Controls',
                    onSelected: (value) {
                      switch (value) {
                        case 'start_fluctuation':
                          provider.startPriceFluctuation();
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('Price fluctuation started!'),
                              backgroundColor: Colors.green,
                            ),
                          );
                          break;
                        case 'stop_fluctuation':
                          provider.stopPriceFluctuation();
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('Price fluctuation stopped!'),
                              backgroundColor: Colors.red,
                            ),
                          );
                          break;
                        case 'fluctuate_once':
                          provider.fluctuatePricesOnce();
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('Prices fluctuated manually!'),
                              backgroundColor: Colors.blue,
                            ),
                          );
                          break;
                        case 'bull_run':
                          provider.simulateMarketEvent(MarketEvent.bullRun);
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text(
                                '🐂 Bull Run simulated! Prices going up!',
                              ),
                              backgroundColor: Colors.green,
                            ),
                          );
                          break;
                        case 'bear_market':
                          provider.simulateMarketEvent(MarketEvent.bearMarket);
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text(
                                '🐻 Bear Market simulated! Prices going down!',
                              ),
                              backgroundColor: Colors.red,
                            ),
                          );
                          break;
                        case 'volatility':
                          provider.simulateMarketEvent(MarketEvent.volatility);
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text(
                                '⚡ High Volatility simulated! Wild price swings!',
                              ),
                              backgroundColor: Colors.orange,
                            ),
                          );
                          break;
                      }
                    },
                    itemBuilder: (context) => [
                      PopupMenuItem(
                        enabled: false,
                        child: Text(
                          'Market Controls',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).primaryColor,
                          ),
                        ),
                      ),
                      const PopupMenuDivider(),
                      PopupMenuItem(
                        value: provider.isPriceFluctuationActive
                            ? 'stop_fluctuation'
                            : 'start_fluctuation',
                        child: Row(
                          children: [
                            Icon(
                              provider.isPriceFluctuationActive
                                  ? Icons.stop
                                  : Icons.play_arrow,
                              color: provider.isPriceFluctuationActive
                                  ? Colors.red
                                  : Colors.green,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              provider.isPriceFluctuationActive
                                  ? 'Stop Auto Fluctuation'
                                  : 'Start Auto Fluctuation',
                            ),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'fluctuate_once',
                        child: Row(
                          children: [
                            Icon(Icons.refresh, color: Colors.blue),
                            SizedBox(width: 8),
                            Text('Fluctuate Once'),
                          ],
                        ),
                      ),
                      const PopupMenuDivider(),
                      const PopupMenuItem(
                        enabled: false,
                        child: Text(
                          'Market Events',
                          style: TextStyle(fontWeight: FontWeight.w600),
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'bull_run',
                        child: Row(
                          children: [
                            Text('🐂', style: TextStyle(fontSize: 16)),
                            SizedBox(width: 8),
                            Text('Bull Run (+2% to +10%)'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'bear_market',
                        child: Row(
                          children: [
                            Text('🐻', style: TextStyle(fontSize: 16)),
                            SizedBox(width: 8),
                            Text('Bear Market (-2% to -10%)'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'volatility',
                        child: Row(
                          children: [
                            Text('⚡', style: TextStyle(fontSize: 16)),
                            SizedBox(width: 8),
                            Text('High Volatility (±15%)'),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
            const SizedBox(width: 8),

            // System update button
            Container(
              decoration: BoxDecoration(
                color: Colors.blue.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: IconButton(
                icon: const Icon(Icons.system_update, color: Colors.blue),
                tooltip: 'System Info',
                onPressed: () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text(
                        'This is Patch 4 with Auto Price Fluctuation!',
                      ),
                      backgroundColor: Colors.blue,
                    ),
                  );
                },
              ),
            ),
            const SizedBox(width: 8),

            // Portfolio button
            Container(
              decoration: BoxDecoration(
                color: Colors.purple.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: IconButton(
                icon: const Icon(
                  Icons.account_balance_wallet,
                  color: Colors.purple,
                ),
                tooltip: 'Portfolio',
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const PortfolioScreen(),
                    ),
                  );
                },
              ),
            ),
            const SizedBox(width: 8),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Stock Market'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Consumer<StockMarketProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (provider.isMaintenanceMode) {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.construction, size: 64, color: Colors.orange),
                  SizedBox(height: 16),
                  Text(
                    'Market is under maintenance',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 8),
                  Text('Please try again later'),
                ],
              ),
            );
          }

          final shares = _getSortedShares(
            provider.shares.values.toList(),
            provider,
          );

          return Column(
            children: [
              // Action buttons row
              _buildActionButtons(),

              // Market status banner
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                color: provider.isMarketOpen
                    ? Colors.green.shade100
                    : Colors.red.shade100,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      provider.isMarketOpen
                          ? Icons.circle
                          : Icons.pause_circle_filled,
                      color: provider.isMarketOpen ? Colors.green : Colors.red,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Market ${provider.marketStatus}',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: provider.isMarketOpen
                            ? Colors.green.shade800
                            : Colors.red.shade800,
                      ),
                    ),
                  ],
                ),
              ),

              // Cash balance
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'Available Cash:',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        Text(
                          '₹${provider.portfolio.cashBalance.toStringAsFixed(2)}',
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.green,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              // Shares list
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: shares.length,
                  itemBuilder: (context, index) {
                    final share = shares[index];
                    return ShareCard(
                      share: share,
                      ownedQuantity: provider.portfolio.getOwnedQuantity(
                        share.symbol,
                      ),
                      onBuy: provider.isMarketOpen
                          ? () => _showBuyDialog(share)
                          : null,
                      onSell:
                          provider.isMarketOpen &&
                              provider.isSellOptionEnabled &&
                              provider.portfolio.ownsShare(share.symbol)
                          ? () => _showSellDialog(share)
                          : null,
                      showMarketTrend: provider.isMarketTrendEnabled,
                    );
                  },
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
