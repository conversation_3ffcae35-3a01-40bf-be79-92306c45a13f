import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shimmer/shimmer.dart';
import 'package:animated_text_kit/animated_text_kit.dart';
import '../models/share.dart';
import '../models/user_portfolio.dart';
import '../providers/stock_market_provider.dart';
import '../providers/auth_provider.dart';
import '../services/price_fluctuation_service.dart';
import '../widgets/share_card.dart';
import '../widgets/buy_sell_dialog.dart';
import 'portfolio_screen.dart';

class ShareListScreen extends StatefulWidget {
  const ShareListScreen({super.key});

  @override
  State<ShareListScreen> createState() => _ShareListScreenState();
}

class _ShareListScreenState extends State<ShareListScreen> {
  String _sortBy = 'symbol'; // symbol, price, change
  bool _sortAscending = true;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<StockMarketProvider>().initialize();
    });
  }

  List<Share> _getSortedShares(
    List<Share> shares,
    StockMarketProvider provider,
  ) {
    if (!provider.isSortingEnabled) return shares;

    final sortedShares = List<Share>.from(shares);

    switch (_sortBy) {
      case 'symbol':
        sortedShares.sort(
          (a, b) => _sortAscending
              ? a.symbol.compareTo(b.symbol)
              : b.symbol.compareTo(a.symbol),
        );
        break;
      case 'price':
        sortedShares.sort(
          (a, b) => _sortAscending
              ? a.currentPrice.compareTo(b.currentPrice)
              : b.currentPrice.compareTo(a.currentPrice),
        );
        break;
      case 'change':
        sortedShares.sort(
          (a, b) => _sortAscending
              ? a.priceChangePercentage.compareTo(b.priceChangePercentage)
              : b.priceChangePercentage.compareTo(a.priceChangePercentage),
        );
        break;
    }

    return sortedShares;
  }

  void _showBuyDialog(Share share) {
    showDialog(
      context: context,
      builder: (context) =>
          BuySellDialog(share: share, transactionType: TransactionType.buy),
    );
  }

  void _showSellDialog(Share share) {
    showDialog(
      context: context,
      builder: (context) =>
          BuySellDialog(share: share, transactionType: TransactionType.sell),
    );
  }

  Widget _buildActionButtons() {
    return Container(
      height: 80,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: [
            // User profile button
            Consumer<AuthProvider>(
              builder: (context, authProvider, child) {
                return Container(
                  margin: const EdgeInsets.only(right: 12),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Theme.of(context).primaryColor,
                        Theme.of(context).primaryColor.withValues(alpha: 0.8),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Theme.of(
                          context,
                        ).primaryColor.withValues(alpha: 0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: PopupMenuButton<String>(
                    icon: Container(
                      padding: const EdgeInsets.all(12),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          CircleAvatar(
                            backgroundColor: Colors.white,
                            radius: 16,
                            child: Text(
                              authProvider.userDisplayName.isNotEmpty
                                  ? authProvider.userDisplayName[0]
                                        .toUpperCase()
                                  : 'U',
                              style: TextStyle(
                                color: Theme.of(context).primaryColor,
                                fontWeight: FontWeight.bold,
                                fontSize: 14,
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            authProvider.userDisplayName.isNotEmpty
                                ? authProvider.userDisplayName.split(' ')[0]
                                : 'User',
                            style: GoogleFonts.roboto(
                              color: Colors.white,
                              fontWeight: FontWeight.w600,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                    onSelected: (value) async {
                      if (value == 'logout') {
                        await authProvider.logout();
                      }
                    },
                    itemBuilder: (context) => [
                      PopupMenuItem(
                        enabled: false,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              authProvider.userDisplayName,
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                            if (authProvider.currentUser != null)
                              Text(
                                authProvider.currentUser!.email,
                                style: TextStyle(
                                  color: Colors.grey[600],
                                  fontSize: 14,
                                ),
                              ),
                          ],
                        ),
                      ),
                      const PopupMenuDivider(),
                      const PopupMenuItem(
                        value: 'logout',
                        child: Row(
                          children: [
                            Icon(Icons.logout, color: Colors.red),
                            SizedBox(width: 8),
                            Text('Logout'),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
            const SizedBox(width: 8),

            // Sort button
            Consumer<StockMarketProvider>(
              builder: (context, provider, child) {
                if (provider.isSortingEnabled) {
                  return Container(
                    decoration: BoxDecoration(
                      color: Theme.of(context).primaryColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: PopupMenuButton<String>(
                      icon: const Icon(Icons.sort),
                      tooltip: 'Sort Options',
                      onSelected: (value) {
                        setState(() {
                          if (_sortBy == value) {
                            _sortAscending = !_sortAscending;
                          } else {
                            _sortBy = value;
                            _sortAscending = true;
                          }
                        });
                      },
                      itemBuilder: (context) => [
                        PopupMenuItem(
                          value: 'symbol',
                          child: Row(
                            children: [
                              const Icon(Icons.abc),
                              const SizedBox(width: 8),
                              const Text('Symbol'),
                              if (_sortBy == 'symbol') ...[
                                const Spacer(),
                                Icon(
                                  _sortAscending
                                      ? Icons.arrow_upward
                                      : Icons.arrow_downward,
                                ),
                              ],
                            ],
                          ),
                        ),
                        PopupMenuItem(
                          value: 'price',
                          child: Row(
                            children: [
                              const Icon(Icons.currency_rupee),
                              const SizedBox(width: 8),
                              const Text('Price'),
                              if (_sortBy == 'price') ...[
                                const Spacer(),
                                Icon(
                                  _sortAscending
                                      ? Icons.arrow_upward
                                      : Icons.arrow_downward,
                                ),
                              ],
                            ],
                          ),
                        ),
                        PopupMenuItem(
                          value: 'change',
                          child: Row(
                            children: [
                              const Icon(Icons.trending_up),
                              const SizedBox(width: 8),
                              const Text('Change %'),
                              if (_sortBy == 'change') ...[
                                const Spacer(),
                                Icon(
                                  _sortAscending
                                      ? Icons.arrow_upward
                                      : Icons.arrow_downward,
                                ),
                              ],
                            ],
                          ),
                        ),
                      ],
                    ),
                  );
                }
                return const SizedBox.shrink();
              },
            ),
            const SizedBox(width: 8),

            // Refresh button
            Container(
              decoration: BoxDecoration(
                color: Colors.blue.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: IconButton(
                icon: const Icon(Icons.refresh, color: Colors.blue),
                tooltip: 'Refresh Prices',
                onPressed: () {
                  context.read<StockMarketProvider>().updateSharePrices();
                },
              ),
            ),
            const SizedBox(width: 8),

            // Market Open/Close Toggle
            Consumer<StockMarketProvider>(
              builder: (context, provider, child) {
                return Container(
                  decoration: BoxDecoration(
                    color: provider.isMarketOpen
                        ? Colors.green.withOpacity(0.1)
                        : Colors.red.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: IconButton(
                    icon: Icon(
                      provider.isMarketOpen
                          ? Icons.store
                          : Icons.store_mall_directory_outlined,
                      color: provider.isMarketOpen ? Colors.green : Colors.red,
                    ),
                    tooltip: provider.isMarketOpen
                        ? 'Close Market'
                        : 'Open Market',
                    onPressed: () async {
                      await provider.toggleMarket();
                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                              provider.isMarketOpen
                                  ? '🟢 Market is now OPEN! Trading enabled.'
                                  : '🔴 Market is now CLOSED! Trading disabled.',
                            ),
                            backgroundColor: provider.isMarketOpen
                                ? Colors.green
                                : Colors.red,
                          ),
                        );
                      }
                    },
                  ),
                );
              },
            ),
            const SizedBox(width: 8),

            // Market control menu
            Consumer<StockMarketProvider>(
              builder: (context, provider, child) {
                return Container(
                  decoration: BoxDecoration(
                    color: provider.isPriceFluctuationActive
                        ? Colors.green.withOpacity(0.1)
                        : Colors.grey.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: PopupMenuButton<String>(
                    icon: Icon(
                      provider.isPriceFluctuationActive
                          ? Icons.trending_up
                          : Icons.trending_flat,
                      color: provider.isPriceFluctuationActive
                          ? Colors.green
                          : Colors.grey,
                    ),
                    tooltip: 'Market Controls',
                    onSelected: (value) {
                      switch (value) {
                        case 'start_fluctuation':
                          provider.startPriceFluctuation();
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('Price fluctuation started!'),
                              backgroundColor: Colors.green,
                            ),
                          );
                          break;
                        case 'stop_fluctuation':
                          provider.stopPriceFluctuation();
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('Price fluctuation stopped!'),
                              backgroundColor: Colors.red,
                            ),
                          );
                          break;
                        case 'fluctuate_once':
                          provider.fluctuatePricesOnce();
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('Prices fluctuated manually!'),
                              backgroundColor: Colors.blue,
                            ),
                          );
                          break;
                        case 'bull_run':
                          provider.simulateMarketEvent(MarketEvent.bullRun);
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text(
                                '🐂 Bull Run simulated! Prices going up!',
                              ),
                              backgroundColor: Colors.green,
                            ),
                          );
                          break;
                        case 'bear_market':
                          provider.simulateMarketEvent(MarketEvent.bearMarket);
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text(
                                '🐻 Bear Market simulated! Prices going down!',
                              ),
                              backgroundColor: Colors.red,
                            ),
                          );
                          break;
                        case 'volatility':
                          provider.simulateMarketEvent(MarketEvent.volatility);
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text(
                                '⚡ High Volatility simulated! Wild price swings!',
                              ),
                              backgroundColor: Colors.orange,
                            ),
                          );
                          break;
                      }
                    },
                    itemBuilder: (context) => [
                      PopupMenuItem(
                        enabled: false,
                        child: Text(
                          'Market Controls',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).primaryColor,
                          ),
                        ),
                      ),
                      const PopupMenuDivider(),
                      PopupMenuItem(
                        value: provider.isPriceFluctuationActive
                            ? 'stop_fluctuation'
                            : 'start_fluctuation',
                        child: Row(
                          children: [
                            Icon(
                              provider.isPriceFluctuationActive
                                  ? Icons.stop
                                  : Icons.play_arrow,
                              color: provider.isPriceFluctuationActive
                                  ? Colors.red
                                  : Colors.green,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              provider.isPriceFluctuationActive
                                  ? 'Stop Auto Fluctuation'
                                  : 'Start Auto Fluctuation',
                            ),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'fluctuate_once',
                        child: Row(
                          children: [
                            Icon(Icons.refresh, color: Colors.blue),
                            SizedBox(width: 8),
                            Text('Fluctuate Once'),
                          ],
                        ),
                      ),
                      const PopupMenuDivider(),
                      const PopupMenuItem(
                        enabled: false,
                        child: Text(
                          'Market Events',
                          style: TextStyle(fontWeight: FontWeight.w600),
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'bull_run',
                        child: Row(
                          children: [
                            Text('🐂', style: TextStyle(fontSize: 16)),
                            SizedBox(width: 8),
                            Text('Bull Run (+2% to +10%)'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'bear_market',
                        child: Row(
                          children: [
                            Text('🐻', style: TextStyle(fontSize: 16)),
                            SizedBox(width: 8),
                            Text('Bear Market (-2% to -10%)'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'volatility',
                        child: Row(
                          children: [
                            Text('⚡', style: TextStyle(fontSize: 16)),
                            SizedBox(width: 8),
                            Text('High Volatility (±15%)'),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
            const SizedBox(width: 8),

            // System update button
            Container(
              decoration: BoxDecoration(
                color: Colors.blue.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: IconButton(
                icon: const Icon(Icons.system_update, color: Colors.blue),
                tooltip: 'System Info',
                onPressed: () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text(
                        'This is Patch 4 with Auto Price Fluctuation!',
                      ),
                      backgroundColor: Colors.blue,
                    ),
                  );
                },
              ),
            ),
            const SizedBox(width: 8),

            // Portfolio button
            Container(
              decoration: BoxDecoration(
                color: Colors.purple.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: IconButton(
                icon: const Icon(
                  Icons.account_balance_wallet,
                  color: Colors.purple,
                ),
                tooltip: 'Portfolio',
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const PortfolioScreen(),
                    ),
                  );
                },
              ),
            ),
            const SizedBox(width: 8),
          ],
        ),
      ),
    );
  }

  Widget _buildAnimatedAppBar() {
    return Consumer<StockMarketProvider>(
      builder: (context, provider, child) {
        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Theme.of(context).primaryColor,
                Theme.of(context).primaryColor.withValues(alpha: 0.8),
                Theme.of(context).colorScheme.secondary,
              ],
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: Row(
                children: [
                  // App Title with Animation
                  Expanded(
                    flex: 2,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          'Stock Market',
                          style: GoogleFonts.poppins(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: provider.isMarketOpen
                                ? Colors.green.withValues(alpha: 0.2)
                                : Colors.red.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: provider.isMarketOpen
                                  ? Colors.green
                                  : Colors.red,
                              width: 1,
                            ),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                provider.isMarketOpen
                                    ? Icons.circle
                                    : Icons.pause_circle_filled,
                                color: provider.isMarketOpen
                                    ? Colors.green
                                    : Colors.red,
                                size: 12,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                provider.marketStatus.toUpperCase(),
                                style: GoogleFonts.roboto(
                                  fontSize: 10,
                                  fontWeight: FontWeight.w600,
                                  color: provider.isMarketOpen
                                      ? Colors.green
                                      : Colors.red,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Balance Display with Animation
                  Expanded(
                    flex: 3,
                    child: Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.15),
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: Colors.white.withValues(alpha: 0.3),
                          width: 1,
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            'Available Balance',
                            style: GoogleFonts.roboto(
                              fontSize: 11,
                              color: Colors.white.withValues(alpha: 0.9),
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(height: 2),
                          AnimatedDefaultTextStyle(
                            duration: const Duration(milliseconds: 300),
                            style: GoogleFonts.poppins(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                const Icon(
                                  Icons.currency_rupee,
                                  color: Colors.white,
                                  size: 16,
                                ),
                                Flexible(
                                  child: AnimatedSwitcher(
                                    duration: const Duration(milliseconds: 500),
                                    child: Text(
                                      provider.portfolio.cashBalance
                                          .toStringAsFixed(2),
                                      key: ValueKey(
                                        provider.portfolio.cashBalance,
                                      ),
                                      style: GoogleFonts.poppins(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.white,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Shimmer.fromColors(
            baseColor: Colors.grey[300]!,
            highlightColor: Colors.grey[100]!,
            child: const CircularProgressIndicator(),
          ),
          const SizedBox(height: 16),
          Text(
            'Loading Market Data...',
            style: GoogleFonts.roboto(fontSize: 16, color: Colors.grey[600]),
          ),
        ],
      ),
    );
  }

  Widget _buildMaintenanceState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.orange.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: const Icon(
              Icons.construction,
              size: 64,
              color: Colors.orange,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'Market is under maintenance',
            style: GoogleFonts.poppins(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.grey[800],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Please try again later',
            style: GoogleFonts.roboto(fontSize: 14, color: Colors.grey[600]),
          ),
        ],
      ),
    );
  }

  Widget _buildSharesList(List<Share> shares, StockMarketProvider provider) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Header with portfolio summary
          Container(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Your Stocks',
                        style: GoogleFonts.poppins(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.grey[800],
                        ),
                      ),
                      Text(
                        '${shares.length} stocks available',
                        style: GoogleFonts.roboto(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [Colors.blue.shade400, Colors.blue.shade600],
                    ),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(
                        Icons.trending_up,
                        color: Colors.white,
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'Live',
                        style: GoogleFonts.roboto(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Shares list
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: shares.length,
              itemBuilder: (context, index) {
                final share = shares[index];
                return Container(
                  margin: const EdgeInsets.only(bottom: 12),
                  child: ShareCard(
                    share: share,
                    ownedQuantity: provider.portfolio.getOwnedQuantity(
                      share.symbol,
                    ),
                    onBuy: provider.isMarketOpen
                        ? () => _showBuyDialog(share)
                        : null,
                    onSell:
                        provider.isMarketOpen &&
                            provider.isSellOptionEnabled &&
                            provider.portfolio.ownsShare(share.symbol)
                        ? () => _showSellDialog(share)
                        : null,
                    showMarketTrend: provider.isMarketTrendEnabled,
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: Column(
        children: [
          _buildAnimatedAppBar(),
          Expanded(
            child: Consumer<StockMarketProvider>(
              builder: (context, provider, child) {
                if (provider.isLoading) {
                  return _buildLoadingState();
                }

                if (provider.isMaintenanceMode) {
                  return _buildMaintenanceState();
                }

                final shares = _getSortedShares(
                  provider.shares.values.toList(),
                  provider,
                );

                return Column(
                  children: [
                    // Action buttons row
                    _buildActionButtons(),

                    // Shares list
                    Expanded(child: _buildSharesList(shares, provider)),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
